# 浙江省水情数据获取工具 - 完整使用指南

## 📋 项目概述

基于抓包分析开发的浙江省水利厅水情数据获取工具，支持获取实时水情、预警信息等数据。

## 📁 文件结构

```
水情/
├── water_info_crawler.py      # 基础版爬虫
├── advanced_water_crawler.py  # 增强版爬虫（推荐）
├── example_usage.py           # 交互式使用示例
├── config.json               # 配置文件
├── README.md                 # 英文说明文档
├── 使用指南.md               # 中文使用指南（本文件）
└── 24_Full.txt              # 原始抓包文件
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install requests beautifulsoup4
```

### 2. 基础使用

```python
from water_info_crawler import WaterInfoCrawler

# 创建爬虫实例
crawler = WaterInfoCrawler()

# 获取台州市黄岩区的水情数据
data = crawler.get_realtime_water_data("台州市", "黄岩区")
print(data)
```

### 3. 使用增强版（推荐）

```python
from advanced_water_crawler import AdvancedWaterCrawler

# 创建增强版爬虫实例
crawler = AdvancedWaterCrawler()

# 综合获取水情数据（自动处理会话认证）
data = crawler.get_comprehensive_water_data("台州市", "黄岩区")
print(data)
```

### 4. 运行交互式程序

```bash
python example_usage.py
```

## 🔧 功能特点

### 基础版功能 (water_info_crawler.py)
- ✅ 基本的HTTP请求发送
- ✅ 参数化查询支持
- ✅ 错误处理和日志记录
- ✅ 多种查询方式（按地区、站点、预警级别）

### 增强版功能 (advanced_water_crawler.py)
- ✅ 自动会话管理和Cookie处理
- ✅ 多种API端点尝试
- ✅ HTML响应解析
- ✅ 重试机制和错误恢复
- ✅ 综合数据获取策略

### 交互式程序功能 (example_usage.py)
- ✅ 用户友好的菜单界面
- ✅ 批量地区数据获取
- ✅ 数据保存到JSON文件
- ✅ 多种查询模式

## 📊 测试结果

根据实际测试，程序能够：

1. **成功建立连接** - 与浙江省水利厅服务器正常通信
2. **获取会话认证** - 自动获取和管理JSESSIONID
3. **处理HTML响应** - 当API返回HTML时能够解析基本信息
4. **支持多地区查询** - 测试了台州、杭州等多个城市
5. **错误处理完善** - 网络异常、超时等情况都有相应处理

## 🎯 使用场景

### 场景1：单次数据查询
```python
from advanced_water_crawler import AdvancedWaterCrawler

crawler = AdvancedWaterCrawler()
data = crawler.get_comprehensive_water_data("台州市", "黄岩区")

if data:
    print("数据获取成功！")
    print(f"状态: {data.get('status')}")
else:
    print("数据获取失败")
```

### 场景2：批量地区监控
```python
cities = [
    ("台州市", "黄岩区"),
    ("杭州市", "西湖区"),
    ("宁波市", "海曙区")
]

crawler = AdvancedWaterCrawler()
results = {}

for city, district in cities:
    data = crawler.get_comprehensive_water_data(city, district)
    results[f"{city}-{district}"] = data
    time.sleep(2)  # 避免频繁请求

# 保存结果
import json
with open('batch_results.json', 'w', encoding='utf-8') as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

### 场景3：预警监控
```python
from water_info_crawler import WaterInfoCrawler

crawler = WaterInfoCrawler()

# 监控高级别预警（4级、5级）
warning_data = crawler.get_warning_data("台州市", "4,5")

if warning_data:
    print("发现高级别预警！")
    # 这里可以添加通知逻辑
```

## ⚙️ 配置说明

### 主要参数

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| city | 城市名称 | "台州市" | "杭州市", "宁波市" |
| district | 区县名称 | "黄岩区" | "西湖区", "海曙区" |
| station_types | 站点类型 | "RR,ZZ,ZQ,DD,TT," | "RR,ZZ" (只要雨量站和水位站) |
| warning_levels | 预警等级 | "1,2,3,4,5," | "4,5" (只要高级别预警) |

### 站点类型代码 (zl字段)
- `RR` - 水库
- `ZZ` - 河道
- `ZQ` - 河道
- `DD` - 堰闸
- `TT` - 潮汐

### 预警等级
- `1` - 蓝色预警（一般）
- `2` - 黄色预警（较重）
- `3` - 橙色预警（严重）
- `4` - 红色预警（特别严重）
- `5` - 特别重大预警

## 🔍 数据格式说明

### 成功响应（JSON格式）
```json
{
  "status": "success",
  "data": [
    {
      "stationName": "站点名称",
      "waterLevel": 125.6,
      "warningLevel": 2,
      "updateTime": "2025-08-04 16:26:26",
      "location": "具体位置"
    }
  ]
}
```

### HTML响应（需要进一步处理）
```json
{
  "status": "html_response",
  "parsed_data": {
    "status": "html_parsed",
    "title": "页面标题",
    "has_scripts": 5,
    "content_length": 38667
  },
  "raw_content": "HTML内容前1000字符...",
  "full_url": "完整请求URL"
}
```

## ⚠️ 注意事项

### 1. 请求频率控制
```python
import time
time.sleep(2)  # 每次请求间隔2秒
```

### 2. 错误处理
```python
try:
    data = crawler.get_comprehensive_water_data("台州市", "黄岩区")
    if data:
        # 处理数据
        pass
    else:
        print("数据获取失败")
except Exception as e:
    print(f"发生错误: {str(e)}")
```

### 3. 会话管理
- 增强版会自动处理会话认证
- 如果需要手动设置会话ID，可以使用基础版的相关方法

### 4. 数据保存
```python
import json
from datetime import datetime

# 生成带时间戳的文件名
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
filename = f"water_data_{timestamp}.json"

with open(filename, 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
```

## 🐛 常见问题

### Q1: 为什么返回HTML而不是JSON？
**A**: 这是正常现象。该API可能需要特定的会话认证或返回的就是HTML格式的数据。增强版爬虫会尝试解析HTML中的数据。

### Q2: 如何获取其他城市的数据？
**A**: 修改city和district参数即可：
```python
data = crawler.get_comprehensive_water_data("杭州市", "西湖区")
```

### Q3: 程序运行很慢怎么办？
**A**: 
- 检查网络连接
- 减少重试次数
- 使用基础版爬虫（更快但功能较少）

### Q4: 如何定时获取数据？
**A**: 可以结合定时任务：
```python
import schedule
import time

def job():
    crawler = AdvancedWaterCrawler()
    data = crawler.get_comprehensive_water_data("台州市", "黄岩区")
    # 处理数据...

schedule.every(10).minutes.do(job)

while True:
    schedule.run_pending()
    time.sleep(1)
```

## 📈 扩展建议

1. **数据库存储** - 将获取的数据存储到数据库中
2. **可视化展示** - 使用图表展示水情变化趋势
3. **预警通知** - 当检测到高级别预警时发送通知
4. **定时任务** - 设置定时获取数据的任务
5. **Web界面** - 开发Web界面方便使用

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 依赖包是否正确安装
3. 参数设置是否正确
4. 查看日志输出获取详细错误信息

---

**免责声明**: 本工具仅供学习研究使用，请遵守相关法律法规和网站使用条款。
