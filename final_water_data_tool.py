#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版水情数据获取工具
结合实际API调用和数据解析
"""

import requests
import json
import re
from datetime import datetime
from typing import Dict, List, Optional


class FinalWaterDataTool:
    """最终版水情数据获取工具"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.main_url = "https://sqfb.slt.zj.gov.cn/"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def initialize_session(self) -> bool:
        """初始化会话"""
        try:
            print("🔐 初始化会话...")
            
            # 访问主页
            response = self.session.get(self.main_url, timeout=30)
            response.raise_for_status()
            
            # 访问水情页面
            water_page_url = f"{self.base_url}/nuxtsyq/"
            response = self.session.get(water_page_url, timeout=30)
            response.raise_for_status()
            
            print(f"   ✅ 会话初始化成功，Cookie: {dict(self.session.cookies)}")
            return True
            
        except Exception as e:
            print(f"   ❌ 会话初始化失败: {str(e)}")
            return False
    
    def get_water_data(self, city: str = "台州市", district: str = "黄岩区") -> Optional[Dict]:
        """获取水情数据"""
        print(f"🌊 获取 {city} {district} 的水情数据...")
        
        # 确保会话已初始化
        if not self.session.cookies:
            if not self.initialize_session():
                return None
        
        # 构建参数（基于您提供的准确参数）
        params = {
            'areaFlag': 1,
            'sss': city,
            'ssx': district,
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
        
        try:
            url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
            
            print(f"   📡 发送请求: {url}")
            print(f"   📋 参数: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            print(f"   ✅ 请求成功: {response.status_code}")
            print(f"   📄 内容长度: {len(response.text)} 字符")
            
            return {
                'status': 'success',
                'html_content': response.text,
                'url': response.url,
                'cookies': dict(self.session.cookies)
            }
            
        except Exception as e:
            print(f"   ❌ 请求失败: {str(e)}")
            return None
    
    def parse_nuxt_data(self, html_content: str) -> Optional[Dict]:
        """解析NUXT数据"""
        print("🔍 解析NUXT数据...")
        
        if not html_content:
            return None
        
        # 查找window.__NUXT__脚本
        if 'window.__NUXT__=' in html_content:
            print("   ✅ 找到NUXT脚本")
            
            # 提取脚本内容
            start_idx = html_content.find('window.__NUXT__=')
            end_idx = html_content.find(';</script>', start_idx)
            
            if start_idx != -1 and end_idx != -1:
                nuxt_script = html_content[start_idx:end_idx]
                print(f"   📜 脚本长度: {len(nuxt_script)} 字符")
                
                # 尝试解析（这里可能需要根据实际情况调整）
                # 由于NUXT数据是压缩的JavaScript，我们返回原始内容
                return {
                    'found': True,
                    'script_content': nuxt_script,
                    'note': '找到NUXT脚本，但需要进一步解析压缩的JavaScript'
                }
        
        print("   ❌ 未找到NUXT脚本")
        return None
    
    def get_formatted_water_data(self, city: str = "台州市", district: str = "黄岩区") -> Dict:
        """获取格式化的水情数据"""
        print(f"🌊 获取 {city} {district} 的格式化水情数据")
        print("="*60)
        
        # 1. 获取原始数据
        raw_response = self.get_water_data(city, district)
        
        result = {
            'query': {
                'city': city,
                'district': district,
                'timestamp': datetime.now().isoformat()
            },
            'api_response': {
                'success': raw_response is not None,
                'status': raw_response.get('status') if raw_response else 'failed',
                'url': raw_response.get('url') if raw_response else None,
                'cookies': raw_response.get('cookies') if raw_response else None
            },
            'parsed_data': None,
            'demo_data': None,
            'summary': {
                'data_source': 'api_call',
                'parsing_status': 'pending'
            }
        }
        
        if raw_response:
            # 2. 尝试解析NUXT数据
            parsed_nuxt = self.parse_nuxt_data(raw_response.get('html_content', ''))
            result['parsed_data'] = parsed_nuxt
            
            if parsed_nuxt and parsed_nuxt.get('found'):
                result['summary']['parsing_status'] = 'nuxt_found_but_compressed'
            else:
                result['summary']['parsing_status'] = 'nuxt_not_found'
        
        # 3. 提供演示数据作为参考
        result['demo_data'] = self.get_demo_data_structure()
        result['summary']['demo_data_available'] = True
        
        return result
    
    def get_demo_data_structure(self) -> Dict:
        """获取演示数据结构（基于抓包文件）"""
        return {
            'note': '这是基于抓包文件的演示数据结构',
            'field_definitions': {
                'zl_types': {
                    'RR': '水库',
                    'ZZ': '河道',
                    'ZQ': '河道',
                    'DD': '堰闸',
                    'TT': '潮汐'
                },
                'sklx_types': {
                    '1': '其他(含小二)',
                    '2': '小一',
                    '3': '中型水库',
                    '4': '大型水库',
                    '5': '大型水库',
                    '9': '其他(含小二)'
                }
            },
            'stations': [
                {
                    '站点编号': '70405712',
                    '站点名称': '飞水岩水库',
                    '所属城市': '台州市',
                    '所属区县': '黄岩区',
                    '当前水位': '153.67 m',
                    '限制水位': '153.60 m',
                    '库容': '0.42 万m³',
                    '经度': 121.097038,
                    '纬度': 28.667364,
                    '更新时间': '2025-08-05T00:15:00'
                },
                {
                    '站点编号': '70405742',
                    '站点名称': '水竹水库',
                    '所属城市': '台州市',
                    '所属区县': '黄岩区',
                    '当前水位': '210.69 m',
                    '限制水位': '210.60 m',
                    '库容': '0.82 万m³',
                    '经度': 121.195051,
                    '纬度': 28.71574,
                    '更新时间': '2025-08-05T00:15:00'
                },
                {
                    '站点编号': '70405744',
                    '站点名称': '黄坦水库',
                    '所属城市': '台州市',
                    '所属区县': '黄岩区',
                    '当前水位': '18.31 m',
                    '限制水位': '18.00 m',
                    '库容': '2.27 万m³',
                    '经度': 121.196961,
                    '纬度': 28.699041,
                    '更新时间': '2025-08-05T00:15:00'
                },
                {
                    '站点编号': '7041JB44',
                    '站点名称': '白沙园水库',
                    '所属城市': '台州市',
                    '所属区县': '黄岩区',
                    '当前水位': '93.38 m',
                    '限制水位': '93.30 m',
                    '库容': '0.18 万m³',
                    '经度': 121.031007,
                    '纬度': 28.495483,
                    '更新时间': '2025-08-05T00:15:00'
                },
                {
                    '站点编号': '7041JB46',
                    '站点名称': '柔极溪二级水库',
                    '所属城市': '台州市',
                    '所属区县': '黄岩区',
                    '当前水位': '354.02 m',
                    '限制水位': '353.97 m',
                    '库容': '0.09 万m³',
                    '经度': 120.940955,
                    '纬度': 28.683597,
                    '更新时间': '2025-08-05T00:15:00'
                }
            ]
        }
    
    def batch_query(self, locations: List[tuple]) -> Dict:
        """批量查询多个地区"""
        print(f"📊 批量查询 {len(locations)} 个地区...")
        
        results = {}
        
        for i, (city, district) in enumerate(locations):
            print(f"\n进度: {i+1}/{len(locations)} - {city} {district}")
            
            try:
                data = self.get_formatted_water_data(city, district)
                results[f"{city}-{district}"] = data
                
                if data['api_response']['success']:
                    print(f"   ✅ 成功")
                else:
                    print(f"   ❌ 失败")
                    
            except Exception as e:
                print(f"   ❌ 异常: {str(e)}")
                results[f"{city}-{district}"] = {
                    'error': str(e),
                    'success': False
                }
            
            # 添加延迟
            if i < len(locations) - 1:
                import time
                time.sleep(2)
        
        return results


def main():
    """主函数"""
    print("🌊 最终版水情数据获取工具")
    print("结合实际API调用和数据解析")
    print("="*60)
    
    tool = FinalWaterDataTool()
    
    # 单个地区查询
    print("1. 单个地区查询测试...")
    result = tool.get_formatted_water_data("台州市", "黄岩区")
    
    print(f"\n📊 查询结果:")
    print(f"   地区: {result['query']['city']} {result['query']['district']}")
    print(f"   时间: {result['query']['timestamp']}")
    print(f"   API调用: {'✅ 成功' if result['api_response']['success'] else '❌ 失败'}")
    print(f"   数据解析: {result['summary']['parsing_status']}")
    print(f"   演示数据: {'✅ 可用' if result['summary']['demo_data_available'] else '❌ 不可用'}")
    
    if result['demo_data']:
        print(f"\n🏭 演示数据 (共 {len(result['demo_data']['stations'])} 个站点):")
        for station in result['demo_data']['stations'][:3]:  # 只显示前3个
            print(f"   - {station['站点名称']}: {station['当前水位']}")
    
    # 保存结果
    output_file = f"final_water_data_{result['query']['city']}_{result['query']['district']}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 结果已保存到: {output_file}")
    
    print(f"\n✅ 测试完成!")
    print(f"\n💡 说明:")
    print(f"   - API调用正常，能够获取到HTML响应")
    print(f"   - HTML中包含NUXT脚本，但是压缩格式")
    print(f"   - 提供了基于抓包文件的演示数据结构")
    print(f"   - 可以根据需要进一步开发JavaScript解析功能")


if __name__ == "__main__":
    main()
