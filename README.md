# 浙江省水情数据获取工具

基于抓包分析开发的浙江省水利厅水情数据获取工具，可以获取实时水情、预警信息等数据。

## 功能特点

- 🌊 获取实时水情数据
- 🚨 监控预警级别信息
- 🔍 按站点名称搜索
- 🏙️ 支持多地区批量查询
- 💾 数据保存到JSON文件
- 🔐 支持会话认证

## 文件说明

- `water_info_crawler.py` - 主要的爬虫类
- `example_usage.py` - 使用示例和交互式程序
- `config.json` - 配置文件（可选）
- `README.md` - 说明文档

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 1. 基本使用

```python
from water_info_crawler import WaterInfoCrawler

# 创建爬虫实例
crawler = WaterInfoCrawler()

# 获取台州市黄岩区的水情数据
data = crawler.get_realtime_water_data("台州市", "黄岩区")
print(data)
```

### 2. 运行交互式程序

```bash
python example_usage.py
```

### 3. 搜索特定站点

```python
# 搜索包含"水库"的站点
station_data = crawler.search_stations_by_name("水库", "台州市")
```

### 4. 获取预警数据

```python
# 获取4级和5级预警数据
warning_data = crawler.get_warning_data("台州市", "4,5")
```

## API 参数说明

### get_realtime_water_data() 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| city | str | "台州市" | 城市名称 |
| district | str | "黄岩区" | 区县名称 |
| area_flag | int | 1 | 区域标志 |
| station_types | str | "RR,ZZ,ZQ,DD,TT," | 站点类型 |
| reservoir_types | str | "4,5,3,1,9,2," | 水库类型 |
| is_over_warning | int | 1 | 是否超警 |
| warning_levels | str | "1,2,3,4,5," | 报汛等级 |
| station_name | str | "" | 站名筛选 |
| collection_source | str | "" | 采集来源 |
| warning_flag | int | 0 | 报汛标志 |

### 站点类型说明

- `RR` - 雨量站
- `ZZ` - 水位站  
- `ZQ` - 流量站
- `DD` - 蒸发站
- `TT` - 气温站

### 水库类型说明

- `1` - 大型水库
- `2` - 中型水库
- `3` - 小型水库
- `4` - 山塘
- `5` - 其他
- `9` - 未知类型

### 预警等级说明

- `1` - 蓝色预警
- `2` - 黄色预警
- `3` - 橙色预警
- `4` - 红色预警
- `5` - 特别重大预警

## 使用注意事项

### 1. 会话认证

如果返回HTML响应而不是JSON数据，可能需要先获取有效的会话ID：

```python
# 使用会话ID
crawler = WaterInfoCrawler()
data = crawler.get_water_data_with_session("your_session_id_here")
```

### 2. 请求频率

建议在请求之间添加适当的延迟，避免频繁请求：

```python
import time
time.sleep(2)  # 等待2秒
```

### 3. 错误处理

程序已包含基本的错误处理，但建议在生产环境中添加更完善的异常处理。

### 4. 数据格式

返回的数据格式可能包括：

- JSON格式的水情数据
- HTML响应（需要会话认证）
- 错误信息

## 示例输出

### 成功获取JSON数据
```json
{
  "status": "success",
  "data": [
    {
      "stationName": "某水库",
      "waterLevel": 125.6,
      "warningLevel": 2,
      "updateTime": "2025-08-04 16:26:26"
    }
  ]
}
```

### HTML响应（需要认证）
```json
{
  "status": "html_response",
  "content": "<!doctype html>...",
  "full_url": "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater?..."
}
```

## 常见问题

### Q: 为什么返回HTML而不是JSON数据？
A: 可能需要有效的会话认证。可以尝试：
1. 先访问主页获取会话ID
2. 使用 `get_water_data_with_session()` 方法
3. 检查请求参数是否正确

### Q: 如何获取其他城市的数据？
A: 修改 `city` 和 `district` 参数即可，例如：
```python
data = crawler.get_realtime_water_data("杭州市", "西湖区")
```

### Q: 如何保存数据到文件？
A: 使用示例程序中的保存功能，或者：
```python
import json
with open('water_data.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
```

## 免责声明

本工具仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用者应当：

1. 合理控制请求频率
2. 不得用于商业用途
3. 遵守数据使用规范
4. 承担使用风险

## 更新日志

- v1.0.0 - 初始版本，支持基本的水情数据获取功能

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件

---

**注意**: 本工具基于抓包分析开发，API可能会发生变化，请及时更新代码。
