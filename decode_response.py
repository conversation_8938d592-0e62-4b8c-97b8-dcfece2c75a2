#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解码压缩的API响应内容
"""

import requests
import gzip
import zlib
try:
    import brotli
except ImportError:
    brotli = None
from optimized_water_crawler import OptimizedWaterCrawler
import json
from bs4 import BeautifulSoup


def decode_compressed_response():
    """解码压缩的响应内容"""
    print("=== 解码压缩的API响应 ===\n")
    
    # 创建一个新的会话，不自动解压缩
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br, zstd',  # 支持压缩
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://sqfb.slt.zj.gov.cn/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    })
    
    # 先获取会话Cookie
    try:
        print("1. 初始化会话...")
        main_response = session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
        print(f"   主页访问: {main_response.status_code}")
        
        water_response = session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
        print(f"   水情页面访问: {water_response.status_code}")
        print(f"   获得Cookie: {dict(session.cookies)}")
        
    except Exception as e:
        print(f"   会话初始化失败: {str(e)}")
        return
    
    # 构建请求参数
    params = {
        'areaFlag': 1,
        'sss': '台州市',
        'ssx': '黄岩区',
        'zl': 'RR,ZZ,ZQ,DD,TT,',
        'sklx': '4,5,3,2,1,9,',
        'sfcj': 1,
        'bxdj': '1,2,3,4,5,',
        'ly': '',
        'zm': '',
        'cjly': '',
        'bx': 0
    }
    
    url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
    
    try:
        print(f"\n2. 发送API请求...")
        print(f"   URL: {url}")
        print(f"   参数: {params}")
        
        # 发送请求，让requests自动处理解压缩
        response = session.get(url, params=params, timeout=30)
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   内容编码: {response.headers.get('Content-Encoding', '无')}")
        print(f"   内容类型: {response.headers.get('Content-Type', '无')}")
        print(f"   内容长度: {len(response.content)} 字节")
        print(f"   文本长度: {len(response.text)} 字符")
        
        # 检查是否是HTML
        if 'text/html' in response.headers.get('Content-Type', ''):
            print(f"\n3. 解析HTML内容...")
            
            # 保存解压后的内容
            with open('decoded_response.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"   ✅ 解压后的HTML已保存到: decoded_response.html")
            
            # 分析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找标题
            title = soup.find('title')
            if title:
                print(f"   📌 页面标题: {title.get_text().strip()}")
            
            # 查找脚本中的数据
            scripts = soup.find_all('script')
            print(f"   📜 找到 {len(scripts)} 个脚本标签")
            
            data_found = False
            for i, script in enumerate(scripts):
                if script.string and len(script.string.strip()) > 100:
                    print(f"      脚本 {i+1}: {len(script.string)} 字符")
                    
                    # 查找可能的JSON数据
                    script_content = script.string
                    
                    # 查找常见的数据模式
                    import re
                    
                    # 查找变量赋值
                    var_matches = re.findall(r'var\s+(\w+)\s*=\s*(\{.*?\});', script_content, re.DOTALL)
                    if var_matches:
                        print(f"         找到变量: {[match[0] for match in var_matches]}")
                        for var_name, var_value in var_matches[:3]:  # 只显示前3个
                            try:
                                parsed = json.loads(var_value)
                                print(f"         {var_name}: 有效JSON数据")
                                print(f"         内容预览: {str(parsed)[:200]}...")
                                data_found = True
                            except:
                                print(f"         {var_name}: 非JSON数据")
                    
                    # 查找数组数据
                    array_matches = re.findall(r'(\[.*?\])', script_content)
                    if array_matches:
                        print(f"         找到 {len(array_matches)} 个数组")
                        for j, array_data in enumerate(array_matches[:2]):  # 只检查前2个
                            try:
                                parsed = json.loads(array_data)
                                if len(parsed) > 0:
                                    print(f"         数组 {j+1}: {len(parsed)} 个元素")
                                    print(f"         元素示例: {str(parsed[0])[:100]}...")
                                    data_found = True
                            except:
                                continue
            
            # 查找表格数据
            tables = soup.find_all('table')
            if tables:
                print(f"   📊 找到 {len(tables)} 个表格")
                for i, table in enumerate(tables):
                    rows = table.find_all('tr')
                    if rows:
                        print(f"      表格 {i+1}: {len(rows)} 行")
                        # 显示表头
                        first_row = rows[0]
                        headers = [cell.get_text().strip() for cell in first_row.find_all(['th', 'td'])]
                        if headers:
                            print(f"         表头: {headers}")
                            data_found = True
            
            # 查找包含水情关键词的内容
            keywords = ['水位', '流量', '雨量', '水库', '河道', '预警', '监测', '站点', 'data', 'list', 'table']
            found_keywords = []
            
            for keyword in keywords:
                if keyword in response.text:
                    count = response.text.count(keyword)
                    if count > 0:
                        found_keywords.append(f"{keyword}({count})")
            
            if found_keywords:
                print(f"   🔍 找到关键词: {', '.join(found_keywords)}")
                data_found = True
            
            # 显示HTML片段
            print(f"\n4. HTML内容预览:")
            print("   " + "="*50)
            print("   " + response.text[:500].replace('\n', '\n   '))
            print("   " + "="*50)
            
            if not data_found:
                print(f"\n❌ 未在HTML中找到明显的数据结构")
                print(f"💡 这可能是一个需要JavaScript渲染的页面")
            else:
                print(f"\n✅ 在HTML中找到了数据结构")
        
        else:
            print(f"\n3. 非HTML响应")
            print(f"   内容预览: {response.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {str(e)}")


if __name__ == "__main__":
    decode_compressed_response()
