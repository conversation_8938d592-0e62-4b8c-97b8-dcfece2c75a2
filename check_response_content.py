#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查API响应内容的详细工具
"""

from optimized_water_crawler import OptimizedWaterCrawler
import json
import re
from bs4 import BeautifulSoup


def analyze_response_content():
    """分析响应内容的详细信息"""
    print("=== 水情API响应内容分析 ===\n")
    
    crawler = OptimizedWaterCrawler()
    
    # 获取台州市黄岩区的数据
    print("正在获取台州市黄岩区的水情数据...")
    data = crawler.get_water_data("台州市", "黄岩区")
    
    if not data:
        print("❌ 数据获取失败")
        return
    
    print(f"✅ 数据获取成功")
    print(f"状态: {data.get('status')}")
    print(f"数据类型: {data.get('data_type')}")
    print(f"内容长度: {data.get('content_length')} 字符")
    print(f"请求URL: {data.get('url')}")
    print(f"会话Cookie: {data.get('cookies')}")
    
    # 获取完整的HTML内容
    html_content = None
    try:
        import requests
        session = requests.Session()
        session.cookies.update(data.get('cookies', {}))
        session.headers.update(crawler.session.headers)
        
        response = session.get(data.get('url'), timeout=30)
        html_content = response.text
        
        print(f"\n📄 完整HTML内容长度: {len(html_content)} 字符")
        
    except Exception as e:
        print(f"❌ 获取完整内容失败: {str(e)}")
        html_content = data.get('content_preview', '')
    
    if html_content:
        print("\n" + "="*60)
        print("📋 HTML内容分析")
        print("="*60)
        
        # 1. 查找页面标题
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"📌 页面标题: {title.get_text().strip()}")
            else:
                print("📌 页面标题: 未找到")
        except Exception as e:
            print(f"📌 页面标题解析失败: {str(e)}")
        
        # 2. 查找可能的数据
        print(f"\n🔍 搜索可能的数据模式...")
        
        # 查找JSON数据
        json_patterns = [
            r'var\s+\w+\s*=\s*(\{.*?\});',
            r'data\s*:\s*(\{.*?\})',
            r'(\{[^{}]*"data"[^{}]*\})',
            r'(\[.*?\])',
        ]
        
        found_data = False
        for i, pattern in enumerate(json_patterns):
            matches = re.findall(pattern, html_content, re.DOTALL)
            if matches:
                print(f"  模式 {i+1}: 找到 {len(matches)} 个匹配")
                for j, match in enumerate(matches[:3]):  # 只显示前3个
                    try:
                        # 尝试解析JSON
                        parsed = json.loads(match)
                        print(f"    匹配 {j+1}: 有效JSON - {str(parsed)[:100]}...")
                        found_data = True
                    except:
                        print(f"    匹配 {j+1}: 非JSON - {match[:50]}...")
        
        if not found_data:
            print("  ❌ 未找到明显的JSON数据")
        
        # 3. 查找表格数据
        print(f"\n📊 搜索表格数据...")
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            tables = soup.find_all('table')
            if tables:
                print(f"  找到 {len(tables)} 个表格")
                for i, table in enumerate(tables[:2]):  # 只分析前2个表格
                    rows = table.find_all('tr')
                    print(f"    表格 {i+1}: {len(rows)} 行")
                    if rows:
                        # 显示表头
                        headers = [th.get_text().strip() for th in rows[0].find_all(['th', 'td'])]
                        if headers:
                            print(f"      表头: {headers}")
            else:
                print("  ❌ 未找到表格")
        except Exception as e:
            print(f"  ❌ 表格分析失败: {str(e)}")
        
        # 4. 查找特定的水情相关关键词
        print(f"\n🌊 搜索水情相关内容...")
        keywords = ['水位', '流量', '雨量', '水库', '河道', '预警', '监测', '站点']
        found_keywords = []
        
        for keyword in keywords:
            if keyword in html_content:
                count = html_content.count(keyword)
                found_keywords.append(f"{keyword}({count})")
        
        if found_keywords:
            print(f"  找到关键词: {', '.join(found_keywords)}")
        else:
            print("  ❌ 未找到水情相关关键词")
        
        # 5. 显示HTML内容片段
        print(f"\n📝 HTML内容预览 (前500字符):")
        print("-" * 50)
        print(html_content[:500])
        print("-" * 50)
        
        # 6. 显示HTML内容片段 (中间部分)
        mid_start = len(html_content) // 2
        print(f"\n📝 HTML内容预览 (中间500字符):")
        print("-" * 50)
        print(html_content[mid_start:mid_start+500])
        print("-" * 50)
        
        # 7. 保存完整HTML到文件
        try:
            with open('response_content.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n💾 完整HTML内容已保存到: response_content.html")
        except Exception as e:
            print(f"\n❌ 保存HTML文件失败: {str(e)}")
    
    print(f"\n" + "="*60)
    print("🎯 分析总结")
    print("="*60)
    print("1. ✅ API连接正常，返回200状态码")
    print("2. ✅ 会话认证成功，获得有效Cookie")
    print("3. ✅ 参数传递正确")
    print("4. ℹ️  返回HTML格式响应（非JSON）")
    print("5. 💡 建议查看保存的HTML文件了解具体内容结构")


if __name__ == "__main__":
    analyze_response_content()
