#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版水情数据获取工具
基于用户提供的准确参数信息优化
"""

import requests
import json
import time
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OptimizedWaterCrawler:
    """优化版水情数据爬虫，基于准确的参数信息"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.main_url = "https://sqfb.slt.zj.gov.cn/"
        self.session = requests.Session()
        
        # 设置标准请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
        
        # 标准参数配置（基于用户提供的准确信息）
        self.default_params = {
            'areaFlag': 1,
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',  # 用户提供的正确顺序
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
    
    def initialize_session(self) -> bool:
        """初始化会话，获取必要的Cookie"""
        try:
            logger.info("正在初始化会话...")
            
            # 访问主页获取初始Cookie
            response = self.session.get(self.main_url, timeout=30)
            response.raise_for_status()
            logger.info(f"主页访问成功，获得Cookie: {dict(self.session.cookies)}")
            
            # 访问水情页面建立完整会话
            water_page_url = f"{self.base_url}/nuxtsyq/"
            response = self.session.get(water_page_url, timeout=30)
            response.raise_for_status()
            logger.info(f"水情页面访问成功，最终Cookie: {dict(self.session.cookies)}")
            
            return True
            
        except Exception as e:
            logger.error(f"会话初始化失败: {str(e)}")
            return False
    
    def get_water_data(self, 
                      city: str = "台州市", 
                      district: str = "黄岩区",
                      station_name: str = "",
                      warning_levels: str = "1,2,3,4,5,",
                      custom_params: Dict = None) -> Optional[Dict]:
        """
        获取水情数据
        
        Args:
            city: 城市名称
            district: 区县名称  
            station_name: 站点名称筛选
            warning_levels: 预警等级筛选
            custom_params: 自定义参数覆盖
            
        Returns:
            返回水情数据或None
        """
        
        # 构建请求参数
        params = self.default_params.copy()
        params.update({
            'sss': city,
            'ssx': district,
            'zm': station_name,
            'bxdj': warning_levels
        })
        
        # 应用自定义参数
        if custom_params:
            params.update(custom_params)
        
        # 确保会话已初始化
        if not self.session.cookies:
            self.initialize_session()
        
        try:
            url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
            
            logger.info(f"正在获取水情数据: {city} - {district}")
            logger.info(f"请求URL: {url}")
            logger.info(f"请求参数: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            logger.info(f"请求成功，状态码: {response.status_code}")
            logger.info(f"响应内容类型: {response.headers.get('Content-Type', 'unknown')}")
            
            content_type = response.headers.get('Content-Type', '')
            
            if 'application/json' in content_type:
                # JSON响应
                return {
                    'status': 'success',
                    'data_type': 'json',
                    'data': response.json(),
                    'url': response.url
                }
            elif 'text/html' in content_type:
                # HTML响应
                return {
                    'status': 'html_response',
                    'data_type': 'html',
                    'content_length': len(response.text),
                    'content_preview': response.text[:500],
                    'url': response.url,
                    'cookies': dict(self.session.cookies)
                }
            else:
                # 其他响应类型
                return {
                    'status': 'unknown_response',
                    'data_type': content_type,
                    'content': response.text[:300],
                    'url': response.url
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return None
    
    def search_by_station(self, station_keyword: str, city: str = "台州市") -> Optional[Dict]:
        """根据站点关键词搜索"""
        return self.get_water_data(
            city=city,
            district="",  # 不限制区县
            station_name=station_keyword
        )
    
    def get_warning_data(self, city: str = "台州市", warning_levels: str = "4,5") -> Optional[Dict]:
        """获取指定预警等级的数据"""
        return self.get_water_data(
            city=city,
            district="",
            warning_levels=warning_levels
        )
    
    def get_all_stations(self, city: str = "台州市") -> Optional[Dict]:
        """获取指定城市的所有站点数据"""
        return self.get_water_data(
            city=city,
            district="",
            warning_levels="1,2,3,4,5,"
        )
    
    def batch_query(self, locations: List[tuple], delay: float = 2.0) -> Dict:
        """
        批量查询多个地区的数据
        
        Args:
            locations: [(city, district), ...] 地区列表
            delay: 请求间隔时间（秒）
            
        Returns:
            批量查询结果字典
        """
        results = {}
        
        for i, (city, district) in enumerate(locations):
            logger.info(f"批量查询进度: {i+1}/{len(locations)} - {city} {district}")
            
            try:
                data = self.get_water_data(city, district)
                results[f"{city}-{district}"] = {
                    'success': data is not None,
                    'data': data
                }
                
                if data:
                    logger.info(f"✓ {city} {district} - 成功")
                else:
                    logger.warning(f"✗ {city} {district} - 失败")
                    
            except Exception as e:
                logger.error(f"✗ {city} {district} - 异常: {str(e)}")
                results[f"{city}-{district}"] = {
                    'success': False,
                    'error': str(e)
                }
            
            # 添加延迟避免频繁请求
            if i < len(locations) - 1:
                time.sleep(delay)
        
        return results


def test_optimized_crawler():
    """测试优化版爬虫"""
    print("=== 优化版水情数据获取工具测试 ===\n")
    
    crawler = OptimizedWaterCrawler()
    
    # 测试1: 基本数据获取
    print("1. 测试基本数据获取...")
    data = crawler.get_water_data("台州市", "黄岩区")
    if data:
        print(f"✓ 成功 - 状态: {data.get('status')}")
        print(f"  数据类型: {data.get('data_type')}")
        print(f"  URL: {data.get('url')}")
        if data.get('cookies'):
            print(f"  会话Cookie: {data.get('cookies')}")
    else:
        print("✗ 失败")
    
    print("\n" + "="*50 + "\n")
    
    # 测试2: 站点搜索
    print("2. 测试站点搜索...")
    data = crawler.search_by_station("水库", "台州市")
    if data:
        print(f"✓ 成功 - 状态: {data.get('status')}")
    else:
        print("✗ 失败")
    
    print("\n" + "="*50 + "\n")
    
    # 测试3: 预警数据
    print("3. 测试预警数据...")
    data = crawler.get_warning_data("台州市", "4,5")
    if data:
        print(f"✓ 成功 - 状态: {data.get('status')}")
    else:
        print("✗ 失败")
    
    print("\n" + "="*50 + "\n")
    
    # 测试4: 批量查询
    print("4. 测试批量查询...")
    locations = [("台州市", "黄岩区"), ("杭州市", "西湖区")]
    results = crawler.batch_query(locations, delay=1.0)
    
    print("批量查询结果:")
    for location, result in results.items():
        status = "✓ 成功" if result['success'] else "✗ 失败"
        print(f"  {location}: {status}")
    
    print("\n=== 测试完成 ===")


def main():
    """主函数"""
    print("优化版水情数据获取工具")
    print("基于用户提供的准确参数信息")
    print("参数配置:")
    print("- areaFlag: 1")
    print("- sss: 该地市")
    print("- ssx: 该区县") 
    print("- zl: RR,ZZ,ZQ,DD,TT,")
    print("- sklx: 4,5,3,2,1,9,")
    print("- sfcj: 1")
    print("- bxdj: 1,2,3,4,5,")
    print()
    
    # 运行测试
    test_optimized_crawler()


if __name__ == "__main__":
    main()
