#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML水情数据解析器
深度解析HTML响应中的水情数据
"""

import requests
import json
import re
from bs4 import BeautifulSoup
from optimized_water_crawler import OptimizedWaterCrawler
import base64
import gzip
import zlib


class WaterDataParser:
    """水情数据解析器"""
    
    def __init__(self):
        self.crawler = OptimizedWaterCrawler()
    
    def get_raw_response(self, city="台州市", district="黄岩区"):
        """获取原始响应数据"""
        print(f"🌊 获取 {city} {district} 的原始响应数据...")
        
        # 使用我们的爬虫获取数据
        response_data = self.crawler.get_water_data(city, district)
        
        if not response_data:
            print("❌ 获取响应失败")
            return None
        
        print(f"✅ 响应获取成功")
        print(f"   状态: {response_data.get('status')}")
        print(f"   数据类型: {response_data.get('data_type')}")
        print(f"   内容长度: {response_data.get('content_length')} 字符")
        
        return response_data
    
    def get_full_html_content(self, city="台州市", district="黄岩区"):
        """获取完整的HTML内容"""
        print(f"📄 获取完整HTML内容...")
        
        # 创建新的会话来获取完整内容
        session = requests.Session()
        session.headers.update(self.crawler.session.headers)
        
        try:
            # 初始化会话
            session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
            
            # 构建参数
            params = {
                'areaFlag': 1,
                'sss': city,
                'ssx': district,
                'zl': 'RR,ZZ,ZQ,DD,TT,',
                'sklx': '4,5,3,2,1,9,',
                'sfcj': 1,
                'bxdj': '1,2,3,4,5,',
                'ly': '',
                'zm': '',
                'cjly': '',
                'bx': 0
            }
            
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            response = session.get(url, params=params, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            print(f"   编码: {response.encoding}")
            print(f"   内容长度: {len(response.text)} 字符")
            
            return response.text
            
        except Exception as e:
            print(f"❌ 获取HTML失败: {str(e)}")
            return None
    
    def parse_javascript_data(self, html_content):
        """解析JavaScript中的数据"""
        print(f"🔍 解析JavaScript数据...")
        
        if not html_content:
            return {}
        
        found_data = {}
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            scripts = soup.find_all('script')
            
            print(f"   找到 {len(scripts)} 个脚本标签")
            
            for i, script in enumerate(scripts):
                if not script.string:
                    continue
                
                script_content = script.string
                print(f"   分析脚本 {i+1}: {len(script_content)} 字符")
                
                # 1. 查找变量赋值
                var_patterns = [
                    r'var\s+(\w+)\s*=\s*(\{.*?\});',
                    r'let\s+(\w+)\s*=\s*(\{.*?\});',
                    r'const\s+(\w+)\s*=\s*(\{.*?\});',
                    r'(\w+)\s*=\s*(\{.*?\});'
                ]
                
                for pattern in var_patterns:
                    matches = re.findall(pattern, script_content, re.DOTALL)
                    for var_name, var_value in matches:
                        try:
                            parsed = json.loads(var_value)
                            if isinstance(parsed, dict) and len(parsed) > 0:
                                found_data[f"var_{var_name}"] = parsed
                                print(f"      ✅ 变量 {var_name}: {len(parsed)} 个键")
                        except:
                            continue
                
                # 2. 查找数组数据
                array_patterns = [
                    r'var\s+(\w+)\s*=\s*(\[.*?\]);',
                    r'let\s+(\w+)\s*=\s*(\[.*?\]);',
                    r'const\s+(\w+)\s*=\s*(\[.*?\]);',
                    r'(\w+)\s*=\s*(\[.*?\]);'
                ]
                
                for pattern in array_patterns:
                    matches = re.findall(pattern, script_content, re.DOTALL)
                    for var_name, var_value in matches:
                        try:
                            parsed = json.loads(var_value)
                            if isinstance(parsed, list) and len(parsed) > 0:
                                found_data[f"array_{var_name}"] = parsed
                                print(f"      ✅ 数组 {var_name}: {len(parsed)} 个元素")
                        except:
                            continue
                
                # 3. 查找特定的水情数据模式
                water_patterns = [
                    r'data\s*:\s*(\{.*?\})',
                    r'list\s*:\s*(\[.*?\])',
                    r'stations\s*:\s*(\[.*?\])',
                    r'waterData\s*:\s*(\{.*?\})',
                    r'realtime\s*:\s*(\[.*?\])'
                ]
                
                for pattern in water_patterns:
                    matches = re.findall(pattern, script_content, re.DOTALL)
                    for match in matches:
                        try:
                            parsed = json.loads(match)
                            if parsed:
                                found_data[f"water_data_{len(found_data)}"] = parsed
                                print(f"      ✅ 水情数据: {type(parsed).__name__}")
                        except:
                            continue
        
        except Exception as e:
            print(f"   ❌ JavaScript解析失败: {str(e)}")
        
        return found_data
    
    def parse_html_tables(self, html_content):
        """解析HTML表格数据"""
        print(f"📊 解析HTML表格...")
        
        if not html_content:
            return []
        
        tables_data = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            tables = soup.find_all('table')
            
            print(f"   找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                print(f"   分析表格 {i+1}...")
                
                table_data = {
                    'table_id': i + 1,
                    'headers': [],
                    'rows': []
                }
                
                # 获取表头
                header_row = table.find('tr')
                if header_row:
                    headers = []
                    for cell in header_row.find_all(['th', 'td']):
                        header_text = cell.get_text().strip()
                        headers.append(header_text)
                    table_data['headers'] = headers
                    print(f"      表头: {headers}")
                
                # 获取数据行
                rows = table.find_all('tr')[1:]  # 跳过表头
                for row in rows:
                    row_data = []
                    for cell in row.find_all(['td', 'th']):
                        cell_text = cell.get_text().strip()
                        row_data.append(cell_text)
                    if row_data:
                        table_data['rows'].append(row_data)
                
                if table_data['rows']:
                    tables_data.append(table_data)
                    print(f"      数据行: {len(table_data['rows'])} 行")
        
        except Exception as e:
            print(f"   ❌ 表格解析失败: {str(e)}")
        
        return tables_data
    
    def search_embedded_data(self, html_content):
        """搜索嵌入的数据"""
        print(f"🔎 搜索嵌入数据...")
        
        if not html_content:
            return {}
        
        embedded_data = {}
        
        # 1. 查找JSON字符串
        json_patterns = [
            r'(\{[^{}]*"[^"]*"[^{}]*:[^{}]*\})',
            r'(\[[^\[\]]*\{[^{}]*\}[^\[\]]*\])',
        ]
        
        for i, pattern in enumerate(json_patterns):
            matches = re.findall(pattern, html_content)
            valid_json_count = 0
            
            for match in matches:
                try:
                    parsed = json.loads(match)
                    if parsed:
                        embedded_data[f"embedded_json_{i}_{valid_json_count}"] = parsed
                        valid_json_count += 1
                except:
                    continue
            
            if valid_json_count > 0:
                print(f"   ✅ 模式 {i+1}: 找到 {valid_json_count} 个有效JSON")
        
        # 2. 查找特定的数据属性
        data_attributes = [
            r'data-(\w+)="([^"]*)"',
            r'value="(\{[^"]*\})"',
            r'content="(\[.*?\])"'
        ]
        
        for pattern in data_attributes:
            matches = re.findall(pattern, html_content)
            for match in matches:
                try:
                    if isinstance(match, tuple) and len(match) == 2:
                        attr_name, attr_value = match
                        try:
                            parsed = json.loads(attr_value)
                            embedded_data[f"attr_{attr_name}"] = parsed
                            print(f"   ✅ 属性 {attr_name}: 有效数据")
                        except:
                            continue
                except:
                    continue
        
        return embedded_data
    
    def extract_water_info(self, city="台州市", district="黄岩区"):
        """提取完整的水情信息"""
        print(f"🌊 开始提取 {city} {district} 的水情数据")
        print("="*60)
        
        # 1. 获取HTML内容
        html_content = self.get_full_html_content(city, district)
        if not html_content:
            return None
        
        # 保存原始HTML
        with open(f'water_data_{city}_{district}.html', 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"💾 原始HTML已保存")
        
        # 2. 解析JavaScript数据
        js_data = self.parse_javascript_data(html_content)
        
        # 3. 解析HTML表格
        table_data = self.parse_html_tables(html_content)
        
        # 4. 搜索嵌入数据
        embedded_data = self.search_embedded_data(html_content)
        
        # 5. 搜索关键词
        keywords = ['水位', '流量', '雨量', '水库', '河道', '预警', '监测', '站点']
        keyword_matches = {}
        
        for keyword in keywords:
            if keyword in html_content:
                # 找到关键词周围的内容
                pattern = f'.{{0,50}}{re.escape(keyword)}.{{0,50}}'
                matches = re.findall(pattern, html_content)
                if matches:
                    keyword_matches[keyword] = matches[:5]  # 只保留前5个匹配
        
        # 6. 组合结果
        result = {
            'city': city,
            'district': district,
            'timestamp': None,
            'javascript_data': js_data,
            'table_data': table_data,
            'embedded_data': embedded_data,
            'keyword_matches': keyword_matches,
            'html_length': len(html_content),
            'summary': {
                'js_objects': len(js_data),
                'tables': len(table_data),
                'embedded_items': len(embedded_data),
                'keywords_found': len(keyword_matches)
            }
        }
        
        # 7. 保存解析结果
        output_file = f'parsed_water_data_{city}_{district}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 解析完成!")
        print(f"   JavaScript对象: {result['summary']['js_objects']}")
        print(f"   HTML表格: {result['summary']['tables']}")
        print(f"   嵌入数据: {result['summary']['embedded_items']}")
        print(f"   关键词匹配: {result['summary']['keywords_found']}")
        print(f"💾 解析结果已保存到: {output_file}")
        
        return result


def main():
    """主函数"""
    print("🌊 水情数据深度解析工具")
    print("="*60)
    
    parser = WaterDataParser()
    
    # 解析台州市黄岩区的数据
    result = parser.extract_water_info("台州市", "黄岩区")
    
    if result:
        print(f"\n🎯 解析结果摘要:")
        print(f"   地区: {result['city']} {result['district']}")
        print(f"   HTML长度: {result['html_length']:,} 字符")
        print(f"   发现的数据结构:")
        
        if result['javascript_data']:
            print(f"   📜 JavaScript数据:")
            for key, data in result['javascript_data'].items():
                print(f"      - {key}: {type(data).__name__}")
                if isinstance(data, dict):
                    print(f"        键: {list(data.keys())[:5]}")
                elif isinstance(data, list):
                    print(f"        元素数: {len(data)}")
        
        if result['table_data']:
            print(f"   📊 表格数据:")
            for table in result['table_data']:
                print(f"      - 表格 {table['table_id']}: {len(table['rows'])} 行")
                if table['headers']:
                    print(f"        表头: {table['headers']}")
        
        if result['keyword_matches']:
            print(f"   🔍 关键词匹配:")
            for keyword, matches in result['keyword_matches'].items():
                print(f"      - {keyword}: {len(matches)} 处")
    
    print(f"\n✅ 解析完成!")


if __name__ == "__main__":
    main()
