#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版水情查询工具
根据用户澄清的bxdj字段说明进行修正
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional


class CorrectedWaterQueryTool:
    """修正版水情查询工具"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.main_url = "https://sqfb.slt.zj.gov.cn/"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
        
        # 标准参数配置（根据用户澄清进行修正）
        self.standard_params = {
            'areaFlag': 1,
            'zl': 'RR,ZZ,ZQ,DD,TT,',      # 所有站点类型
            'sklx': '4,5,3,2,1,9,',       # 所有水库类型
            'sfcj': 1,                    # 是否超警
            'bxdj': '1,2,3,4,5,',         # 报汛等级（固定值）
            'ly': '',                     # 来源
            'zm': '',                     # 站名
            'cjly': '',                   # 采集来源
            'bx': 0                       # 报汛标志
        }
        
        # 字段定义
        self.field_definitions = {
            'station_types': {
                'RR': '水库',
                'ZZ': '河道',
                'ZQ': '河道',
                'DD': '堰闸',
                'TT': '潮汐'
            },
            'reservoir_types': {
                '1': '其他(含小二)',
                '2': '小一',
                '3': '中型水库',
                '4': '大型水库',
                '5': '大型水库',
                '9': '其他(含小二)'
            }
        }
    
    def initialize_session(self) -> bool:
        """初始化会话"""
        try:
            print("🔐 初始化会话...")
            
            # 访问主页
            response = self.session.get(self.main_url, timeout=30)
            response.raise_for_status()
            
            # 访问水情页面
            water_page_url = f"{self.base_url}/nuxtsyq/"
            response = self.session.get(water_page_url, timeout=30)
            response.raise_for_status()
            
            print(f"   ✅ 会话初始化成功")
            return True
            
        except Exception as e:
            print(f"   ❌ 会话初始化失败: {str(e)}")
            return False
    
    def query_water_data(self, 
                        city: str = "台州市", 
                        district: str = "黄岩区",
                        station_types: List[str] = None,
                        reservoir_types: List[str] = None,
                        station_name: str = "") -> Dict:
        """
        查询水情数据
        
        Args:
            city: 城市名称
            district: 区县名称（为空表示不限制区县）
            station_types: 站点类型列表，如['RR', 'ZZ']
            reservoir_types: 水库类型列表，如['4', '5']
            station_name: 站点名称筛选
            
        Returns:
            查询结果字典
        """
        
        # 构建参数
        params = self.standard_params.copy()
        params.update({
            'sss': city,
            'ssx': district,
            'zm': station_name
        })
        
        # 设置站点类型
        if station_types:
            params['zl'] = ','.join(station_types) + ','
        
        # 设置水库类型
        if reservoir_types:
            params['sklx'] = ','.join(reservoir_types) + ','
        
        return self._execute_query(params, f"{city}_{district}_查询")
    
    def query_reservoirs_only(self, city: str = "台州市", district: str = "黄岩区") -> Dict:
        """只查询水库"""
        print(f"🏗️ 查询水库: {city} {district}")
        return self.query_water_data(city, district, station_types=['RR'])
    
    def query_rivers_only(self, city: str = "台州市", district: str = "黄岩区") -> Dict:
        """只查询河道"""
        print(f"🌊 查询河道: {city} {district}")
        return self.query_water_data(city, district, station_types=['ZZ', 'ZQ'])
    
    def query_large_reservoirs(self, city: str = "台州市", district: str = "") -> Dict:
        """查询大型水库"""
        print(f"🏗️ 查询大型水库: {city}")
        return self.query_water_data(city, district, station_types=['RR'], reservoir_types=['4', '5'])
    
    def query_medium_large_reservoirs(self, city: str = "台州市", district: str = "") -> Dict:
        """查询中型及以上水库"""
        print(f"🏗️ 查询中型及以上水库: {city}")
        return self.query_water_data(city, district, station_types=['RR'], reservoir_types=['3', '4', '5'])
    
    def search_by_name(self, station_name: str, city: str = "台州市") -> Dict:
        """按站点名称搜索"""
        print(f"🔍 按名称搜索: {station_name}")
        return self.query_water_data(city, "", station_name=station_name)
    
    def query_all_stations(self, city: str = "台州市", district: str = "黄岩区") -> Dict:
        """查询所有站点"""
        print(f"📊 查询所有站点: {city} {district}")
        return self.query_water_data(city, district)
    
    def _execute_query(self, params: Dict, query_name: str) -> Dict:
        """执行查询"""
        # 确保会话已初始化
        if not self.session.cookies:
            if not self.initialize_session():
                return {'error': '会话初始化失败'}
        
        try:
            url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
            
            print(f"   📡 发送请求...")
            print(f"   📋 参数: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            result = {
                'query_name': query_name,
                'query_params': params,
                'response': {
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'url': response.url,
                    'success': True
                },
                'timestamp': datetime.now().isoformat(),
                'note': 'bxdj字段固定为1,2,3,4,5，不需要调整'
            }
            
            print(f"   ✅ 查询成功: {response.status_code}")
            print(f"   📄 内容长度: {len(response.text)} 字符")
            print(f"   🔗 完整URL: {response.url}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 查询失败: {str(e)}")
            return {
                'query_name': query_name,
                'query_params': params,
                'response': {
                    'success': False,
                    'error': str(e)
                },
                'timestamp': datetime.now().isoformat()
            }
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🌊 修正版水情查询工具 - 综合测试")
        print("="*60)
        print("📋 重要说明: bxdj字段固定为'1,2,3,4,5,'，不需要动态调整")
        print("="*60)
        
        results = {}
        
        # 1. 查询所有站点
        print("\n1. 查询台州市黄岩区所有站点...")
        results['所有站点'] = self.query_all_stations("台州市", "黄岩区")
        
        # 2. 只查询水库
        print("\n2. 只查询水库...")
        results['水库'] = self.query_reservoirs_only("台州市", "黄岩区")
        
        # 3. 只查询河道
        print("\n3. 只查询河道...")
        results['河道'] = self.query_rivers_only("台州市", "黄岩区")
        
        # 4. 查询大型水库
        print("\n4. 查询台州市大型水库...")
        results['大型水库'] = self.query_large_reservoirs("台州市", "")
        
        # 5. 按名称搜索
        print("\n5. 搜索包含'水库'的站点...")
        results['名称搜索'] = self.search_by_name("水库", "台州市")
        
        # 保存结果
        output_file = f"corrected_query_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {output_file}")
        
        # 显示摘要
        print(f"\n📊 测试摘要:")
        for query_name, result in results.items():
            if result.get('response', {}).get('success'):
                content_length = result.get('response', {}).get('content_length', 0)
                print(f"   ✅ {query_name}: 成功 ({content_length:,} 字符)")
            else:
                print(f"   ❌ {query_name}: 失败")
        
        print(f"\n💡 关键发现:")
        print(f"   - bxdj参数在所有查询中都固定为'1,2,3,4,5,'")
        print(f"   - 可以通过调整zl和sklx参数来筛选不同类型的站点")
        print(f"   - zm参数支持按站点名称进行模糊搜索")
        
        return results


def main():
    """主函数"""
    print("🌊 修正版水情查询工具")
    print("根据用户澄清的bxdj字段说明进行修正")
    print("="*60)
    
    # 显示修正说明
    print("📋 重要修正:")
    print("   bxdj字段（报汛等级）固定为'1,2,3,4,5,'")
    print("   不需要动态调整，所有查询都使用相同的bxdj值")
    print()
    
    # 创建工具并运行测试
    tool = CorrectedWaterQueryTool()
    results = tool.run_comprehensive_test()
    
    print(f"\n✅ 测试完成!")
    print(f"\n🎯 使用建议:")
    print(f"   1. 使用 query_water_data() 进行自定义查询")
    print(f"   2. 使用 query_reservoirs_only() 只查询水库")
    print(f"   3. 使用 query_large_reservoirs() 查询大型水库")
    print(f"   4. 使用 search_by_name() 按名称搜索")
    print(f"   5. bxdj参数始终保持'1,2,3,4,5,'不变")


if __name__ == "__main__":
    main()
