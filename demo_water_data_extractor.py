#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示水情数据提取器
基于您提供的抓包文件中的实际数据格式
"""

import json
from datetime import datetime
from optimized_water_crawler import OptimizedWaterCrawler


class DemoWaterDataExtractor:
    """演示水情数据提取器"""
    
    def __init__(self):
        self.crawler = OptimizedWaterCrawler()
    
    def parse_sample_data(self):
        """解析示例数据（基于您提供的抓包文件）"""
        print("🌊 解析示例水情数据")
        print("="*60)
        
        # 基于您提供的抓包文件中的实际数据
        sample_data = {
            "layout": "default",
            "data": [{
                "waterData": {
                    "cjj": [],  # 采集井
                    "qt": [],   # 其他
                    "cx": [     # 测站
                        {
                            "index": 1,
                            "zh": "70405712",
                            "city": "台州市",
                            "county": "黄岩区", 
                            "time": "2025-08-05T00:15:00",
                            "name": "飞水岩水库",
                            "jj": "-",
                            "bz": "-",
                            "zc": "-0.0",
                            "xx": "153.60",
                            "kr": "0.42",
                            "sw": "153.67",
                            "zcsw": "-",
                            "lon": 121.097038,
                            "lat": 28.667364,
                            "info": {
                                "zh": "70405712",
                                "zm": "飞水岩水库",
                                "ly": "椒江水系",
                                "ITEM": "PZ",
                                "class": " 0",
                                "xzqhm": "331003",
                                "wd": 28.667364,
                                "jd": 121.097038,
                                "zl": "RR",
                                "cjly": "1",
                                "pro": "浙江省",
                                "sss": "台州市",
                                "ssx": "黄岩区",
                                "zcsw": None,
                                "sklx": "1",
                                "bdgc": None,
                                "kr": 0.42,
                                "zc": -0.02,
                                "sbsj": "2025-08-05T00:15:00",
                                "jjsw": None,
                                "bzsw": None,
                                "ztgc": None,
                                "ytgc": None,
                                "OBHTZ": None,
                                "OBHTZTM": None,
                                "xxsw": 153.6,
                                "sw": "153.67"
                            },
                            "counties": "黄岩区"
                        },
                        {
                            "index": 2,
                            "zh": "70405742",
                            "city": "台州市",
                            "county": "黄岩区",
                            "time": "2025-08-05T00:15:00", 
                            "name": "水竹水库",
                            "jj": "-",
                            "bz": "-",
                            "zc": "-0.0",
                            "xx": "210.60",
                            "kr": "0.82",
                            "sw": "210.69",
                            "zcsw": "-",
                            "lon": 121.195051,
                            "lat": 28.71574,
                            "info": {
                                "zh": "70405742",
                                "zm": "水竹水库",
                                "ly": "椒江水系",
                                "ITEM": "PZ",
                                "class": " 0",
                                "xzqhm": "331003",
                                "wd": 28.71574,
                                "jd": 121.195051,
                                "zl": "RR",
                                "cjly": "1",
                                "pro": "浙江省",
                                "sss": "台州市",
                                "ssx": "黄岩区",
                                "zcsw": None,
                                "sklx": "2",
                                "bdgc": None,
                                "kr": 0.82,
                                "zc": -0.01,
                                "sbsj": "2025-08-05T00:15:00",
                                "jjsw": None,
                                "bzsw": None,
                                "ztgc": None,
                                "ytgc": None,
                                "OBHTZ": None,
                                "OBHTZTM": None,
                                "xxsw": 210.6,
                                "sw": "210.69"
                            },
                            "counties": "黄岩区"
                        },
                        {
                            "index": 3,
                            "zh": "70405744",
                            "city": "台州市",
                            "county": "黄岩区",
                            "time": "2025-08-05T00:15:00",
                            "name": "黄坦水库",
                            "jj": "-",
                            "bz": "-", 
                            "zc": "0.0",
                            "xx": "18.00",
                            "kr": "2.27",
                            "sw": "18.31",
                            "zcsw": "-",
                            "lon": 121.196961,
                            "lat": 28.699041,
                            "info": {
                                "zh": "70405744",
                                "zm": "黄坦水库",
                                "ly": "椒江水系",
                                "ITEM": "PZ",
                                "class": " 0",
                                "xzqhm": "331003",
                                "wd": 28.699041,
                                "jd": 121.196961,
                                "zl": "RR",
                                "cjly": "1",
                                "pro": "浙江省",
                                "sss": "台州市",
                                "ssx": "黄岩区",
                                "zcsw": None,
                                "sklx": "2",
                                "bdgc": None,
                                "kr": 2.27,
                                "zc": 0.01,
                                "sbsj": "2025-08-05T00:15:00",
                                "jjsw": None,
                                "bzsw": None,
                                "ztgc": None,
                                "ytgc": None,
                                "OBHTZ": None,
                                "OBHTZTM": None,
                                "xxsw": 18,
                                "sw": "18.31"
                            },
                            "counties": "黄岩区"
                        },
                        {
                            "index": 4,
                            "zh": "7041JB44",
                            "city": "台州市",
                            "county": "黄岩区",
                            "time": "2025-08-05T00:15:00",
                            "name": "白沙园水库",
                            "jj": "-",
                            "bz": "-",
                            "zc": "-0.0",
                            "xx": "93.30",
                            "kr": "0.18",
                            "sw": "93.38",
                            "zcsw": "-",
                            "lon": 121.031007,
                            "lat": 28.495483,
                            "info": {
                                "zh": "7041JB44",
                                "zm": "白沙园水库",
                                "ly": "椒江水系",
                                "ITEM": "PZ",
                                "class": None,
                                "xzqhm": "331003",
                                "wd": 28.495483,
                                "jd": 121.031007,
                                "zl": "RR",
                                "cjly": "1",
                                "pro": "浙江省",
                                "sss": "台州市",
                                "ssx": "黄岩区",
                                "zcsw": None,
                                "sklx": "1",
                                "bdgc": None,
                                "kr": 0.18,
                                "zc": -0.01,
                                "sbsj": "2025-08-05T00:15:00",
                                "jjsw": None,
                                "bzsw": None,
                                "ztgc": None,
                                "ytgc": None,
                                "OBHTZ": None,
                                "OBHTZTM": None,
                                "xxsw": 93.3,
                                "sw": "93.38"
                            },
                            "counties": "黄岩区"
                        },
                        {
                            "index": 5,
                            "zh": "7041JB46",
                            "city": "台州市",
                            "county": "黄岩区",
                            "time": "2025-08-05T00:15:00",
                            "name": "柔极溪二级水库",
                            "jj": "-",
                            "bz": "-",
                            "zc": "-0.1",
                            "xx": "353.97",
                            "kr": "0.09",
                            "sw": "354.02",
                            "zcsw": "-",
                            "lon": 120.940955,
                            "lat": 28.683597,
                            "info": {
                                "zh": "7041JB46",
                                "zm": "柔极溪二级水库",
                                "ly": "椒江水系",
                                "ITEM": "PZ",
                                "class": None,
                                "xzqhm": "331003",
                                "wd": 28.683597,
                                "jd": 120.940955,
                                "zl": "RR",
                                "cjly": "1",
                                "pro": "浙江省",
                                "sss": "台州市",
                                "ssx": "黄岩区",
                                "zcsw": None,
                                "sklx": "1",
                                "bdgc": None,
                                "kr": 0.09,
                                "zc": -0.05,
                                "sbsj": "2025-08-05T00:15:00",
                                "jjsw": None,
                                "bzsw": None,
                                "ztgc": None,
                                "ytgc": None,
                                "OBHTZ": None,
                                "OBHTZTM": None,
                                "xxsw": 353.97,
                                "sw": "354.02"
                            },
                            "counties": "黄岩区"
                        }
                    ],
                    "cbz": []  # 超标站
                },
                "ThData": "3.88(2025-08-05 00:00:00)"
            }],
            "fetch": {},
            "error": None,
            "serverRendered": True,
            "routePath": "/new/realtimeWater",
            "config": {
                "_app": {
                    "basePath": "/nuxtsyq/",
                    "assetsPath": "/nuxtsyq/_nuxt/",
                    "cdnURL": None
                }
            }
        }
        
        return sample_data
    
    def format_water_stations(self, data):
        """格式化水情站点数据"""
        print("📋 格式化水情站点数据...")
        
        if not data or not data.get('data'):
            return []
        
        water_data = data['data'][0].get('waterData', {})
        all_stations = []
        
        # 处理不同类型的站点
        station_types = {
            'cx': '测站',
            'cjj': '采集井',
            'qt': '其他',
            'cbz': '超标站'
        }
        
        for type_key, type_name in station_types.items():
            stations = water_data.get(type_key, [])
            print(f"   📍 {type_name}: {len(stations)} 个")
            
            for station in stations:
                formatted_station = {
                    '站点类型': type_name,
                    '站点编号': station.get('zh', ''),
                    '站点名称': station.get('name', ''),
                    '所属省份': station.get('info', {}).get('pro', ''),
                    '所属城市': station.get('city', ''),
                    '所属区县': station.get('county', ''),
                    '水系': station.get('info', {}).get('ly', ''),
                    '更新时间': station.get('time', ''),
                    '当前水位': f"{station.get('sw', 0)} m",
                    '限制水位': f"{station.get('xx', 0)} m",
                    '库容': f"{station.get('kr', 0)} 万m³",
                    '涨潮值': f"{station.get('zc', 0)} m",
                    '经度': station.get('lon', 0),
                    '纬度': station.get('lat', 0),
                    '行政区划码': station.get('info', {}).get('xzqhm', ''),
                    '站点类别': station.get('info', {}).get('zl', ''),
                    '水库类型': station.get('info', {}).get('sklx', ''),
                    '原始数据': station
                }
                all_stations.append(formatted_station)
        
        return all_stations
    
    def get_demo_water_data(self):
        """获取演示水情数据"""
        print("🌊 获取演示水情数据")
        print("="*60)
        
        # 1. 解析示例数据
        raw_data = self.parse_sample_data()
        
        # 2. 格式化站点数据
        stations = self.format_water_stations(raw_data)
        
        # 3. 组装结果
        result = {
            'query': {
                'city': '台州市',
                'district': '黄岩区',
                'timestamp': datetime.now().isoformat(),
                'data_source': '抓包文件示例数据'
            },
            'raw_data': raw_data,
            'stations': stations,
            'summary': {
                'total_stations': len(stations),
                'th_data': raw_data['data'][0].get('ThData', ''),
                'station_types': {}
            }
        }
        
        # 统计站点类型
        for station in stations:
            station_type = station['站点类型']
            if station_type not in result['summary']['station_types']:
                result['summary']['station_types'][station_type] = 0
            result['summary']['station_types'][station_type] += 1
        
        # 4. 保存结果
        output_file = 'demo_water_data_台州市_黄岩区.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"💾 演示数据已保存到: {output_file}")
        
        return result
    
    def display_results(self, result):
        """显示结果"""
        print(f"\n🎯 水情数据解析结果:")
        print(f"   查询地区: {result['query']['city']} {result['query']['district']}")
        print(f"   数据来源: {result['query']['data_source']}")
        print(f"   查询时间: {result['query']['timestamp']}")
        print(f"   总站点数: {result['summary']['total_stations']}")
        print(f"   汇总数据: {result['summary']['th_data']}")
        
        print(f"\n📊 站点类型统计:")
        for station_type, count in result['summary']['station_types'].items():
            print(f"   {station_type}: {count} 个")
        
        print(f"\n🏭 站点详情:")
        for i, station in enumerate(result['stations']):
            print(f"   站点 {i+1}: {station['站点名称']}")
            print(f"      编号: {station['站点编号']}")
            print(f"      类型: {station['站点类型']}")
            print(f"      水位: {station['当前水位']}")
            print(f"      库容: {station['库容']}")
            print(f"      位置: {station['经度']}, {station['纬度']}")
            print(f"      更新: {station['更新时间']}")
            print()


def main():
    """主函数"""
    print("🌊 演示水情数据提取器")
    print("基于抓包文件中的实际数据格式")
    print("="*60)
    
    extractor = DemoWaterDataExtractor()
    
    # 获取演示数据
    result = extractor.get_demo_water_data()
    
    # 显示结果
    extractor.display_results(result)
    
    print("✅ 演示完成!")
    print("\n💡 说明:")
    print("   这是基于您提供的抓包文件中的实际数据格式")
    print("   实际使用时，需要从API响应中解析出类似的数据结构")
    print("   数据包含了台州市黄岩区的5个水库的实时水情信息")


if __name__ == "__main__":
    main()
