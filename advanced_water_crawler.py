#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版水情数据获取工具
支持会话管理和数据解析
"""

import requests
import json
import re
import time
from bs4 import BeautifulSoup
from typing import Dict, List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AdvancedWaterCrawler:
    """增强版水情数据爬虫"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.main_url = "https://sqfb.slt.zj.gov.cn/"
        self.session = requests.Session()
        self.session_initialized = False
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def initialize_session(self) -> bool:
        """初始化会话，获取必要的Cookie"""
        try:
            logger.info("正在初始化会话...")
            
            # 首先访问主页获取初始Cookie
            response = self.session.get(self.main_url, timeout=30)
            response.raise_for_status()
            
            logger.info(f"主页访问成功，状态码: {response.status_code}")
            logger.info(f"获得的Cookie: {dict(self.session.cookies)}")
            
            # 尝试访问水情页面
            water_page_url = f"{self.base_url}/nuxtsyq/"
            response = self.session.get(water_page_url, timeout=30)
            response.raise_for_status()
            
            logger.info(f"水情页面访问成功，状态码: {response.status_code}")
            logger.info(f"更新后的Cookie: {dict(self.session.cookies)}")
            
            self.session_initialized = True
            return True
            
        except Exception as e:
            logger.error(f"会话初始化失败: {str(e)}")
            return False
    
    def extract_data_from_html(self, html_content: str) -> Optional[Dict]:
        """从HTML响应中提取数据"""
        try:
            # 尝试查找页面中的JavaScript数据
            # 查找可能包含数据的script标签
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含数据的script标签
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # 查找JSON数据模式
                    json_matches = re.findall(r'(\{[^{}]*"data"[^{}]*\})', script.string)
                    for match in json_matches:
                        try:
                            data = json.loads(match)
                            if 'data' in data:
                                return data
                        except:
                            continue
                    
                    # 查找其他可能的数据模式
                    data_matches = re.findall(r'var\s+\w+\s*=\s*(\{.*?\});', script.string, re.DOTALL)
                    for match in data_matches:
                        try:
                            data = json.loads(match)
                            if isinstance(data, dict) and len(data) > 0:
                                return data
                        except:
                            continue
            
            # 如果没有找到JSON数据，返回页面基本信息
            title = soup.find('title')
            title_text = title.text if title else "未知页面"
            
            return {
                'status': 'html_parsed',
                'title': title_text,
                'has_scripts': len(scripts),
                'content_length': len(html_content)
            }
            
        except Exception as e:
            logger.error(f"HTML解析失败: {str(e)}")
            return None
    
    def get_water_data_with_retry(self, **kwargs) -> Optional[Dict]:
        """带重试机制的数据获取"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 如果会话未初始化，先初始化
                if not self.session_initialized:
                    if not self.initialize_session():
                        logger.warning("会话初始化失败，使用默认会话")
                
                # 构建请求参数
                params = {
                    'areaFlag': kwargs.get('area_flag', 1),
                    'sss': kwargs.get('city', '台州市'),
                    'ssx': kwargs.get('district', '黄岩区'),
                    'zl': kwargs.get('station_types', 'RR,ZZ,ZQ,DD,TT,'),
                    'sklx': kwargs.get('reservoir_types', '4,5,3,2,1,9,'),
                    'ly': kwargs.get('source', ''),
                    'sfcj': kwargs.get('is_over_warning', 1),
                    'bxdj': kwargs.get('warning_levels', '1,2,3,4,5,'),
                    'zm': kwargs.get('station_name', ''),
                    'cjly': kwargs.get('collection_source', ''),
                    'bx': kwargs.get('warning_flag', 0)
                }
                
                # 设置Referer
                self.session.headers['Referer'] = f"{self.base_url}/nuxtsyq/"
                
                url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
                
                logger.info(f"尝试第 {attempt + 1} 次请求...")
                logger.info(f"请求URL: {url}")
                logger.info(f"请求参数: {params}")
                
                response = self.session.get(url, params=params, timeout=30)
                response.raise_for_status()
                
                logger.info(f"请求成功，状态码: {response.status_code}")
                logger.info(f"响应内容类型: {response.headers.get('Content-Type', 'unknown')}")
                
                content_type = response.headers.get('Content-Type', '')
                
                if 'application/json' in content_type:
                    # JSON响应
                    return response.json()
                elif 'text/html' in content_type:
                    # HTML响应，尝试解析
                    logger.info("收到HTML响应，尝试解析数据...")
                    parsed_data = self.extract_data_from_html(response.text)
                    
                    if parsed_data:
                        return {
                            'status': 'html_response',
                            'parsed_data': parsed_data,
                            'raw_content': response.text[:1000],
                            'full_url': response.url
                        }
                    else:
                        return {
                            'status': 'html_response',
                            'content': response.text[:1000],
                            'full_url': response.url
                        }
                else:
                    return {
                        'status': 'unknown_response',
                        'content_type': content_type,
                        'content': response.text[:500]
                    }
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"第 {attempt + 1} 次请求失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                else:
                    logger.error("所有重试都失败了")
                    return None
            except Exception as e:
                logger.error(f"未知错误: {str(e)}")
                return None
        
        return None
    
    def try_api_endpoints(self, **kwargs) -> Optional[Dict]:
        """尝试不同的API端点"""
        endpoints = [
            "/nuxtsyq/new/realtimeWater",
            "/nuxtsyq/api/realtimeWater", 
            "/api/realtimeWater",
            "/nuxtsyq/realtimeWater"
        ]
        
        for endpoint in endpoints:
            try:
                logger.info(f"尝试端点: {endpoint}")
                
                params = {
                    'areaFlag': kwargs.get('area_flag', 1),
                    'sss': kwargs.get('city', '台州市'),
                    'ssx': kwargs.get('district', '黄岩区'),
                    'zl': kwargs.get('station_types', 'RR,ZZ,ZQ,DD,TT,'),
                    'sklx': kwargs.get('reservoir_types', '4,5,3,2,1,9,'),
                    'ly': kwargs.get('source', ''),
                    'sfcj': kwargs.get('is_over_warning', 1),
                    'bxdj': kwargs.get('warning_levels', '1,2,3,4,5,'),
                    'zm': kwargs.get('station_name', ''),
                    'cjly': kwargs.get('collection_source', ''),
                    'bx': kwargs.get('warning_flag', 0)
                }
                
                url = f"{self.base_url}{endpoint}"
                response = self.session.get(url, params=params, timeout=15)
                
                if response.status_code == 200:
                    content_type = response.headers.get('Content-Type', '')
                    
                    if 'application/json' in content_type:
                        logger.info(f"端点 {endpoint} 返回JSON数据")
                        return response.json()
                    elif 'text/html' in content_type:
                        logger.info(f"端点 {endpoint} 返回HTML数据")
                        parsed_data = self.extract_data_from_html(response.text)
                        if parsed_data and parsed_data.get('status') != 'html_parsed':
                            return parsed_data
                
            except Exception as e:
                logger.warning(f"端点 {endpoint} 失败: {str(e)}")
                continue
        
        logger.warning("所有端点都失败了")
        return None
    
    def get_comprehensive_water_data(self, city: str = "台州市", district: str = "黄岩区") -> Optional[Dict]:
        """综合获取水情数据的方法"""
        logger.info(f"开始综合获取 {city} {district} 的水情数据")
        
        # 方法1: 标准请求
        logger.info("方法1: 标准请求")
        result = self.get_water_data_with_retry(city=city, district=district)
        if result and result.get('status') != 'html_response':
            return result
        
        # 方法2: 尝试不同端点
        logger.info("方法2: 尝试不同API端点")
        result = self.try_api_endpoints(city=city, district=district)
        if result:
            return result
        
        # 方法3: 重新初始化会话后再试
        logger.info("方法3: 重新初始化会话")
        self.session_initialized = False
        if self.initialize_session():
            result = self.get_water_data_with_retry(city=city, district=district)
            if result:
                return result
        
        logger.warning("所有方法都失败了")
        return None


def test_advanced_crawler():
    """测试增强版爬虫"""
    crawler = AdvancedWaterCrawler()
    
    print("=== 增强版水情数据获取测试 ===\n")
    
    # 测试台州市黄岩区
    print("1. 测试台州市黄岩区...")
    data = crawler.get_comprehensive_water_data("台州市", "黄岩区")
    if data:
        print("✓ 数据获取成功")
        print(f"数据类型: {data.get('status', 'json_data')}")
        if 'parsed_data' in data:
            print(f"解析数据: {json.dumps(data['parsed_data'], ensure_ascii=False, indent=2)}")
        else:
            print(f"数据预览: {str(data)[:300]}...")
    else:
        print("✗ 数据获取失败")
    
    print("\n" + "="*50 + "\n")
    
    # 测试杭州市西湖区
    print("2. 测试杭州市西湖区...")
    data = crawler.get_comprehensive_water_data("杭州市", "西湖区")
    if data:
        print("✓ 数据获取成功")
        print(f"数据类型: {data.get('status', 'json_data')}")
    else:
        print("✗ 数据获取失败")


if __name__ == "__main__":
    test_advanced_crawler()
