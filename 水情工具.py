#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省水情数据获取工具 - 超级简洁版
一个文件搞定所有功能
"""

import requests
from datetime import datetime


class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        
    def 获取数据(self, 城市="台州市", 区县="黄岩区", 类型="全部"):
        """
        获取水情数据

        """
        
        # 初始化会话
        try:
            self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=10)
            self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=10)
        except:
            return {"错误": "网络连接失败"}
        
        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': 城市,
            'ssx': 区县,
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,     
            'bxdj': '1,2,3,4,5,',  # 固定值
            'ly': '',
            'zm': '',  # 站名固定为空
            'cjly': '',
            'bx': 0
        }
        
        
        # 发送请求
        try:
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)
            
            return {
                "状态": "成功",
                "数据长度": len(响应.text),
                "查询参数": 参数,
                "URL": 响应.url
            }
        except Exception as e:
            return {"错误": str(e)}


def main():
    """主函数"""
    print("🌊 浙江省水情数据获取工具")
    print("=" * 40)

    # 配置参数
    城市 = "台州市"
    区县 = "黄岩区"
    类型 = "全部"

    工具 = 水情工具()

    print(f"\n查询: {城市} {区县 or '全市'} {类型}")
    结果 = 工具.获取数据(城市, 区县, 类型)

    if "错误" in 结果:
        print(f"❌ {结果['错误']}")
    else:
        print(f"✅ 成功 - 数据长度: {结果['数据长度']:,} 字符")

    print(f"\n📋 字段说明:")
    print(f"站点类型: RR=水库, ZZ/ZQ=河道, DD=堰闸, TT=潮汐")
    print(f"水库类型: 4/5=大型, 3=中型, 2=小一, 1/9=其他")
    print(f"bxdj固定为'1,2,3,4,5,'")


if __name__ == "__main__":
    main()
