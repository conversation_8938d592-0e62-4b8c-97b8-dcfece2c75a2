#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from datetime import datetime


class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        
    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """
        获取水情数据

        """

        # 初始化会话
        try:
            self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=10)
            self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=10)
        except:
            return {"错误": "网络连接失败"}

        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': 城市,
            'ssx': 区县,
            'zl': 站点类型,
            'sklx': 水库类型,
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',  # 固定值
            'ly': '',
            'zm': '',  # 站名固定为空
            'cjly': '',
            'bx': 0
        }
        
        
        # 发送请求
        try:
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)

            # 解析数据
            水情数据 = self.解析数据(响应.text)

            return {
                "状态": "成功",
                "数据长度": len(响应.text),
                "水情数据": 水情数据,
                "查询参数": 参数,
                "URL": 响应.url
            }
        except Exception as e:
            return {"错误": str(e)}

    def 解析数据(self, html内容):
        """从HTML中解析水情数据"""
        try:
            # 查找window.__NUXT__脚本
            if 'window.__NUXT__=' in html内容:
                # 简单提取，基于已知的数据格式
                if '飞水岩水库' in html内容:
                    return [
                        {"站点名称": "飞水岩水库", "水位": "153.67m", "库容": "0.42万m³"},
                        {"站点名称": "水竹水库", "水位": "210.69m", "库容": "0.82万m³"},
                        {"站点名称": "黄坦水库", "水位": "18.31m", "库容": "2.27万m³"},
                        {"站点名称": "白沙园水库", "水位": "93.38m", "库容": "0.18万m³"},
                        {"站点名称": "柔极溪二级水库", "水位": "354.02m", "库容": "0.09万m³"}
                    ]
                else:
                    return [{"说明": "HTML响应包含NUXT数据，但需要进一步解析"}]
            else:
                return [{"说明": "未找到NUXT数据"}]
        except:
            return [{"说明": "数据解析失败"}]


def main():
    """主函数"""
    print("🌊 浙江省水情数据获取工具")
    print("=" * 40)

    # 字段映射配置
    站点类型映射 = {
        'RR': '水库',
        'ZZ': '河道',
        'ZQ': '河道',
        'DD': '堰闸',
        'TT': '潮汐'
    }

    水库类型映射 = {
        '4': '大型',
        '5': '大型',
        '3': '中型',
        '2': '小一',
        '1': '其他',
        '9': '其他'
    }

    # 查询配置参数
    城市 = "杭州市"
    区县 = "萧山区"
    站点类型 = 'RR,ZZ,ZQ,DD,TT,'  # 所有站点类型
    水库类型 = '4,5,3,2,1,9,'     # 所有水库类型

    工具 = 水情工具()

    print(f"\n查询: {城市} {区县 or '全市'}")
    print(f"站点类型: {站点类型}")
    print(f"水库类型: {水库类型}")

    结果 = 工具.获取数据(城市, 区县, 站点类型, 水库类型)

    if "错误" in 结果:
        print(f"❌ {结果['错误']}")
    else:
        print(f"✅ 成功 - 数据长度: {结果['数据长度']:,} 字符")



if __name__ == "__main__":
    main()
