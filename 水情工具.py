#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from datetime import datetime


class 水情工具:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
        
    def 获取数据(self, 城市, 区县, 站点类型, 水库类型):
        """
        获取水情数据

        """

        # 初始化会话
        try:
            self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=10)
            self.session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=10)
        except:
            return {"错误": "网络连接失败"}

        # 设置参数
        参数 = {
            'areaFlag': 1,
            'sss': 城市,
            'ssx': 区县,
            'zl': 站点类型,
            'sklx': 水库类型,
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',  # 固定值
            'ly': '',
            'zm': '',  # 站名固定为空
            'cjly': '',
            'bx': 0
        }
        
        
        # 发送请求
        try:
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)

            # 保存HTML内容用于分析
            with open('response.html', 'w', encoding='utf-8') as f:
                f.write(响应.text)

            # 解析数据
            水情数据 = self.解析数据(响应.text)

            return {
                "状态": "成功",
                "数据长度": len(响应.text),
                "水情数据": 水情数据,
                "查询参数": 参数,
                "URL": 响应.url
            }
        except Exception as e:
            return {"错误": str(e)}

    def 解析数据(self, html内容):
        """从HTML中解析水情数据"""
        import re

        try:
            # 查找window.__NUXT__脚本
            if 'window.__NUXT__=' not in html内容:
                return [{"说明": "未找到NUXT数据"}]

            # 提取NUXT脚本内容
            start_idx = html内容.find('window.__NUXT__=')
            end_idx = html内容.find(';</script>', start_idx)

            if start_idx == -1 or end_idx == -1:
                return [{"说明": "NUXT脚本格式异常"}]

            nuxt_script = html内容[start_idx:end_idx]

            # 查找函数参数部分（实际数据在最后的括号中）
            # 格式：(null,"萧山区","-","70119585","响天岭水库","331.40",120.111111,29.974167,"1")

            # 直接查找最后的参数部分
            # 格式：(null,"萧山区","-","70119585","响天岭水库","331.40",120.111111,29.974167,"1")
            param_pattern = r'\(null,"([^"]*)","-","([^"]*)","([^"]*?)","([^"]*)",([0-9.]+),([0-9.]+),"([^"]*)"\)'
            param_match = re.search(param_pattern, nuxt_script)

            if not param_match:
                return [{"说明": f"未找到NUXT参数，脚本长度: {len(nuxt_script)}"}]

            # 提取匹配的参数
            区县, 站点ID, 站点名称, 水位, 经度, 纬度, 类型 = param_match.groups()

            # 从NUXT脚本中提取更多信息
            # 查找xx(限制水位)和kr(库容)
            xx_match = re.search(r'xx:"([^"]*)"', nuxt_script)
            kr_match = re.search(r'kr:"([^"]*)"', nuxt_script)

            限制水位 = xx_match.group(1) if xx_match else "N/A"
            库容 = kr_match.group(1) if kr_match else "N/A"

            return [{
                "区县": 区县,
                "站点ID": 站点ID,
                "站点名称": 站点名称,
                "当前水位": f"{水位}m",
                "限制水位": f"{限制水位}m",
                "库容": f"{库容}万m³",
                "经度": float(经度),
                "纬度": float(纬度),
                "类型": 类型
            }]

        except Exception as e:
            return [{"说明": f"数据解析异常: {str(e)}"}]


def main():
    """主函数"""
    print("🌊 浙江省水情数据获取工具")
    print("=" * 40)

    # 字段映射配置
    站点类型映射 = {
        'RR': '水库',
        'ZZ': '河道',
        'ZQ': '河道',
        'DD': '堰闸',
        'TT': '潮汐'
    }

    水库类型映射 = {
        '4': '大型水库',
        '5': '大型水库',
        '3': '中型水库',
        '2': '小一',
        '1': '其他(含小二)',
        '9': '其他(含小二)'
    }

    # 查询配置参数
    城市 = "杭州市"
    区县 = "萧山区"
    站点类型 = 'RR,ZZ,ZQ,DD,TT,'  # 所有站点类型
    水库类型 = '4,5,3,2,1,9,'     # 所有水库类型

    工具 = 水情工具()

    print(f"\n查询: {城市} {区县 or '全市'}")
    print(f"站点类型: {站点类型}")
    print(f"水库类型: {水库类型}")

    结果 = 工具.获取数据(城市, 区县, 站点类型, 水库类型)

    if "错误" in 结果:
        print(f"❌ {结果['错误']}")
    else:
        print(f"✅ 成功 - 数据长度: {结果['数据长度']:,} 字符")

        # 显示解析的水情数据
        水情数据 = 结果.get('水情数据', [])
        if 水情数据:
            print(f"\n🌊 解析出的水情数据:")
            for i, 数据 in enumerate(水情数据, 1):
                if '站点名称' in 数据:
                    print(f"  {i}. {数据['站点名称']}: {数据.get('当前水位', 'N/A')} (库容: {数据.get('库容', 'N/A')})")
                    print(f"     限制水位: {数据.get('限制水位', 'N/A')}, 位置: {数据.get('经度', 0)}, {数据.get('纬度', 0)}")
                else:
                    print(f"  {i}. {数据}")
        else:
            print(f"\n⚠️ 未解析出具体数据")



if __name__ == "__main__":
    main()
