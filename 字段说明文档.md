# 浙江省水情数据字段说明文档

## 📋 API参数说明

基于用户提供的准确信息，以下是各个字段的详细说明：

### 🔧 请求参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `areaFlag` | int | 1 | 区域标志 |
| `sss` | string | "台州市" | 城市名称 |
| `ssx` | string | "黄岩区" | 区县名称 |
| `zl` | string | "RR,ZZ,ZQ,DD,TT," | 站点类型（多选，逗号分隔） |
| `sklx` | string | "4,5,3,2,1,9," | 水库类型（多选，逗号分隔） |
| `sfcj` | int | 1 | 是否超警 |
| `bxdj` | string | "1,2,3,4,5," | 报汛等级（多选，逗号分隔） |
| `ly` | string | "" | 来源 |
| `zm` | string | "" | 站名筛选 |
| `cjly` | string | "" | 采集来源 |
| `bx` | int | 0 | 报汛标志 |

## 🏭 站点类型说明 (zl字段)

| 代码 | 名称 | 说明 |
|------|------|------|
| `RR` | 水库 | 水库监测站点 |
| `ZZ` | 河道 | 河道监测站点 |
| `ZQ` | 河道 | 河道监测站点（另一类型） |
| `DD` | 堰闸 | 堰闸监测站点 |
| `TT` | 潮汐 | 潮汐监测站点 |

### 使用示例
```python
# 只获取水库数据
params = {'zl': 'RR,'}

# 获取河道和堰闸数据
params = {'zl': 'ZZ,ZQ,DD,'}

# 获取所有类型
params = {'zl': 'RR,ZZ,ZQ,DD,TT,'}
```

## 🏗️ 水库类型说明 (sklx字段)

| 代码 | 名称 | 说明 |
|------|------|------|
| `1` | 其他(含小二) | 其他类型水库，包含小二型水库 |
| `2` | 小一 | 小一型水库 |
| `3` | 中型水库 | 中型水库 |
| `4` | 大型水库 | 大型水库 |
| `5` | 大型水库 | 大型水库（另一分类） |
| `9` | 其他(含小二) | 其他类型水库，包含小二型水库 |

### 水库规模分类
- **大型水库** (4,5): 库容较大的重要水库
- **中型水库** (3): 中等规模的水库
- **小一型水库** (2): 小型水库中的较大者
- **其他水库** (1,9): 包含小二型及其他类型水库

### 使用示例
```python
# 只获取大型水库
params = {'sklx': '4,5,'}

# 获取中型和大型水库
params = {'sklx': '3,4,5,'}

# 获取所有类型
params = {'sklx': '4,5,3,2,1,9,'}
```

## 🚨 预警等级说明 (bxdj字段)

| 等级 | 颜色 | 说明 |
|------|------|------|
| `1` | 蓝色 | 一般预警 |
| `2` | 黄色 | 较重预警 |
| `3` | 橙色 | 严重预警 |
| `4` | 红色 | 特别严重预警 |
| `5` | 特级 | 特别重大预警 |

## 📊 响应数据字段说明

### 站点基本信息
| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| `zh` | 站点编号 | "70405712" |
| `name` | 站点名称 | "飞水岩水库" |
| `city` | 所属城市 | "台州市" |
| `county` | 所属区县 | "黄岩区" |
| `time` | 更新时间 | "2025-08-05T00:15:00" |

### 水情数据
| 字段名 | 说明 | 单位 | 示例值 |
|--------|------|------|--------|
| `sw` | 当前水位 | 米(m) | "153.67" |
| `xx` | 限制水位 | 米(m) | "153.60" |
| `kr` | 库容 | 万立方米 | "0.42" |
| `zc` | 涨潮值 | 米(m) | "-0.0" |

### 地理位置
| 字段名 | 说明 | 单位 | 示例值 |
|--------|------|------|--------|
| `lon` | 经度 | 度 | 121.097038 |
| `lat` | 纬度 | 度 | 28.667364 |

### 详细信息 (info字段)
| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| `ly` | 水系 | "椒江水系" |
| `pro` | 省份 | "浙江省" |
| `xzqhm` | 行政区划码 | "331003" |
| `zl` | 站点类别 | "RR" |
| `sklx` | 水库类型 | "1" |
| `cjly` | 采集来源 | "1" |

## 🔍 查询策略建议

### 1. 按地区查询
```python
# 查询特定城市区县
params = {
    'sss': '台州市',
    'ssx': '黄岩区'
}
```

### 2. 按站点类型查询
```python
# 只查询水库
params = {'zl': 'RR,'}

# 查询河道和堰闸
params = {'zl': 'ZZ,ZQ,DD,'}
```

### 3. 按水库规模查询
```python
# 只查询大型水库
params = {'sklx': '4,5,'}

# 查询中型以上水库
params = {'sklx': '3,4,5,'}
```

### 4. 按预警等级查询
```python
# 只查询高级别预警
params = {'bxdj': '4,5,'}

# 查询所有预警
params = {'bxdj': '1,2,3,4,5,'}
```

### 5. 按站点名称查询
```python
# 搜索包含"水库"的站点
params = {'zm': '水库'}

# 搜索特定站点
params = {'zm': '飞水岩'}
```

## 💡 实际应用示例

### 监控大型水库水位
```python
params = {
    'areaFlag': 1,
    'sss': '台州市',
    'ssx': '',  # 不限制区县
    'zl': 'RR,',  # 只要水库
    'sklx': '4,5,',  # 只要大型水库
    'sfcj': 1,
    'bxdj': '1,2,3,4,5,',
    'ly': '',
    'zm': '',
    'cjly': '',
    'bx': 0
}
```

### 监控河道预警情况
```python
params = {
    'areaFlag': 1,
    'sss': '台州市',
    'ssx': '黄岩区',
    'zl': 'ZZ,ZQ,',  # 河道站点
    'sklx': '4,5,3,2,1,9,',
    'sfcj': 1,
    'bxdj': '3,4,5,',  # 只关注高级别预警
    'ly': '',
    'zm': '',
    'cjly': '',
    'bx': 0
}
```

### 全面监控某地区
```python
params = {
    'areaFlag': 1,
    'sss': '台州市',
    'ssx': '黄岩区',
    'zl': 'RR,ZZ,ZQ,DD,TT,',  # 所有类型
    'sklx': '4,5,3,2,1,9,',  # 所有规模
    'sfcj': 1,
    'bxdj': '1,2,3,4,5,',  # 所有预警等级
    'ly': '',
    'zm': '',
    'cjly': '',
    'bx': 0
}
```

## 📝 注意事项

1. **参数格式**: 多选参数需要用逗号分隔，末尾建议加逗号
2. **地区限制**: `ssx`为空时表示不限制区县
3. **站名搜索**: `zm`支持模糊匹配
4. **数据更新**: 数据更新频率约为15分钟一次
5. **坐标系统**: 经纬度使用WGS84坐标系

---

**更新时间**: 2025-08-05  
**数据来源**: 浙江省水利厅水情监测系统  
**API地址**: https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater
