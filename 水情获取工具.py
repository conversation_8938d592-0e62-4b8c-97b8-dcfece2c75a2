#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浙江省水情数据获取工具 - 最终简洁版
基于抓包分析和用户提供的准确字段定义
"""

import requests
import json
from datetime import datetime


class 水情获取工具:
    """浙江省水情数据获取工具"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://sqfb.slt.zj.gov.cn/'
        })
    
    def 初始化会话(self):
        """初始化会话，获取Cookie"""
        try:
            # 访问主页
            self.session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            # 访问水情页面
            self.session.get(f"{self.base_url}/nuxtsyq/", timeout=30)
            return True
        except:
            return False
    
    def 获取水情数据(self, 城市="台州市", 区县="黄岩区", 站点类型=None, 水库类型=None, 站点名称=""):
        """
        获取水情数据
        
        参数:
            城市: 城市名称，如"台州市"
            区县: 区县名称，如"黄岩区"，为空表示不限制
            站点类型: 站点类型列表，如["RR"]表示水库，["ZZ","ZQ"]表示河道
            水库类型: 水库类型列表，如["4","5"]表示大型水库
            站点名称: 站点名称关键词，支持模糊搜索
        
        返回:
            查询结果字典
        """
        
        # 确保会话已初始化
        if not self.session.cookies:
            if not self.初始化会话():
                return {"错误": "会话初始化失败"}
        
        # 构建查询参数
        参数 = {
            'areaFlag': 1,
            'sss': 城市,
            'ssx': 区县,
            'zl': 'RR,ZZ,ZQ,DD,TT,',      # 默认所有站点类型
            'sklx': '4,5,3,2,1,9,',       # 默认所有水库类型
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',         # 固定值，不需要调整
            'ly': '',
            'zm': 站点名称,
            'cjly': '',
            'bx': 0
        }
        
        # 设置站点类型
        if 站点类型:
            参数['zl'] = ','.join(站点类型) + ','
        
        # 设置水库类型
        if 水库类型:
            参数['sklx'] = ','.join(水库类型) + ','
        
        try:
            url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
            响应 = self.session.get(url, params=参数, timeout=30)
            响应.raise_for_status()
            
            return {
                "状态": "成功",
                "查询参数": 参数,
                "响应长度": len(响应.text),
                "完整URL": 响应.url,
                "时间戳": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "状态": "失败",
                "错误": str(e),
                "查询参数": 参数
            }
    
    def 查询水库(self, 城市="台州市", 区县="黄岩区"):
        """查询水库"""
        return self.获取水情数据(城市, 区县, 站点类型=["RR"])
    
    def 查询河道(self, 城市="台州市", 区县="黄岩区"):
        """查询河道"""
        return self.获取水情数据(城市, 区县, 站点类型=["ZZ", "ZQ"])
    
    def 查询大型水库(self, 城市="台州市", 区县=""):
        """查询大型水库"""
        return self.获取水情数据(城市, 区县, 站点类型=["RR"], 水库类型=["4", "5"])
    
    def 按名称搜索(self, 关键词, 城市="台州市"):
        """按站点名称搜索"""
        return self.获取水情数据(城市, "", 站点名称=关键词)
    
    def 批量查询(self, 地区列表):
        """批量查询多个地区"""
        结果 = {}
        
        for 城市, 区县 in 地区列表:
            print(f"正在查询: {城市} {区县}")
            结果[f"{城市}-{区县}"] = self.获取水情数据(城市, 区县)
            
            # 添加延迟避免频繁请求
            import time
            time.sleep(2)
        
        return 结果


def 演示数据():
    """返回基于抓包文件的演示数据"""
    return {
        "说明": "这是基于抓包文件解析出的台州市黄岩区实际水情数据",
        "更新时间": "2025-08-05T00:15:00",
        "水库数据": [
            {
                "站点编号": "70405712",
                "站点名称": "飞水岩水库",
                "当前水位": "153.67 m",
                "限制水位": "153.60 m",
                "库容": "0.42 万m³",
                "经度": 121.097038,
                "纬度": 28.667364
            },
            {
                "站点编号": "70405742", 
                "站点名称": "水竹水库",
                "当前水位": "210.69 m",
                "限制水位": "210.60 m",
                "库容": "0.82 万m³",
                "经度": 121.195051,
                "纬度": 28.71574
            },
            {
                "站点编号": "70405744",
                "站点名称": "黄坦水库", 
                "当前水位": "18.31 m",
                "限制水位": "18.00 m",
                "库容": "2.27 万m³",
                "经度": 121.196961,
                "纬度": 28.699041
            },
            {
                "站点编号": "7041JB44",
                "站点名称": "白沙园水库",
                "当前水位": "93.38 m", 
                "限制水位": "93.30 m",
                "库容": "0.18 万m³",
                "经度": 121.031007,
                "纬度": 28.495483
            },
            {
                "站点编号": "7041JB46",
                "站点名称": "柔极溪二级水库",
                "当前水位": "354.02 m",
                "限制水位": "353.97 m", 
                "库容": "0.09 万m³",
                "经度": 120.940955,
                "纬度": 28.683597
            }
        ]
    }


def 字段说明():
    """返回字段说明"""
    return {
        "站点类型_zl字段": {
            "RR": "水库",
            "ZZ": "河道",
            "ZQ": "河道", 
            "DD": "堰闸",
            "TT": "潮汐"
        },
        "水库类型_sklx字段": {
            "1": "其他(含小二)",
            "2": "小一",
            "3": "中型水库",
            "4": "大型水库", 
            "5": "大型水库",
            "9": "其他(含小二)"
        },
        "重要说明": "bxdj字段固定为'1,2,3,4,5,'，不需要调整"
    }


def main():
    """主函数 - 演示如何使用"""
    print("🌊 浙江省水情数据获取工具")
    print("="*50)
    
    # 创建工具实例
    工具 = 水情获取工具()
    
    print("📋 字段说明:")
    说明 = 字段说明()
    for 类型, 定义 in 说明.items():
        if isinstance(定义, dict):
            print(f"  {类型}:")
            for 代码, 名称 in 定义.items():
                print(f"    {代码}: {名称}")
        else:
            print(f"  {定义}")
    
    print(f"\n🎯 使用示例:")
    
    # 示例1: 查询水库
    print(f"\n1. 查询台州市黄岩区水库...")
    结果1 = 工具.查询水库("台州市", "黄岩区")
    if 结果1.get("状态") == "成功":
        print(f"   ✅ 成功 - 响应长度: {结果1['响应长度']:,} 字符")
    else:
        print(f"   ❌ 失败: {结果1.get('错误')}")
    
    # 示例2: 查询大型水库
    print(f"\n2. 查询台州市大型水库...")
    结果2 = 工具.查询大型水库("台州市")
    if 结果2.get("状态") == "成功":
        print(f"   ✅ 成功 - 响应长度: {结果2['响应长度']:,} 字符")
    else:
        print(f"   ❌ 失败: {结果2.get('错误')}")
    
    # 示例3: 按名称搜索
    print(f"\n3. 搜索包含'水库'的站点...")
    结果3 = 工具.按名称搜索("水库", "台州市")
    if 结果3.get("状态") == "成功":
        print(f"   ✅ 成功 - 响应长度: {结果3['响应长度']:,} 字符")
    else:
        print(f"   ❌ 失败: {结果3.get('错误')}")
    
    # 显示演示数据
    print(f"\n🌊 演示数据 (基于抓包文件):")
    演示 = 演示数据()
    print(f"   更新时间: {演示['更新时间']}")
    print(f"   水库数量: {len(演示['水库数据'])} 个")
    
    for i, 水库 in enumerate(演示['水库数据'][:3], 1):
        print(f"   {i}. {水库['站点名称']}: {水库['当前水位']}")
    
    print(f"\n✅ 演示完成!")
    print(f"\n💡 使用说明:")
    print(f"   - 所有查询都会自动处理会话认证")
    print(f"   - bxdj参数固定为'1,2,3,4,5,'")
    print(f"   - 支持按城市、区县、类型、名称等多种方式查询")
    print(f"   - 返回的HTML响应包含完整的水情数据")


if __name__ == "__main__":
    main()
