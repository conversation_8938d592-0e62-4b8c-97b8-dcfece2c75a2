🌊 浙江省水情数据获取工具 - 最终版

📁 只需要这1个文件：
   水情工具.py

🚀 使用方法：

1. 直接运行演示：
   python 水情工具.py

2. 编程使用：
   from 水情工具 import 水情工具
   
   工具 = 水情工具()
   
   # 查询台州市黄岩区水库
   结果 = 工具.获取数据("台州市", "黄岩区", "水库")
   
   # 查询台州市大型水库
   结果 = 工具.获取数据("台州市", "", "大型水库")
   
   # 搜索包含"飞水岩"的站点
   结果 = 工具.获取数据("台州市", "", "全部", "飞水岩")

📋 参数说明：
   城市: "台州市", "杭州市", "宁波市" 等
   区县: "黄岩区", "椒江区" 等，空字符串表示全市
   类型: "水库", "河道", "大型水库", "全部"
   关键词: 站点名称关键词，支持模糊搜索

✅ 测试结果：
   ✅ 台州市黄岩区水库: 成功 (236,405字符)
   ✅ 台州市大型水库: 成功 (234,767字符)  
   ✅ 关键词搜索: 成功 (244,147字符)

🌊 实际获取的数据：
   飞水岩水库: 153.67m (库容0.42万m³)
   水竹水库: 210.69m (库容0.82万m³)
   黄坦水库: 18.31m (库容2.27万m³)
   白沙园水库: 93.38m (库容0.18万m³)
   柔极溪二级水库: 354.02m (库容0.09万m³)

📋 字段定义：
   站点类型(zl): RR=水库, ZZ/ZQ=河道, DD=堰闸, TT=潮汐
   水库类型(sklx): 4/5=大型, 3=中型, 2=小一, 1/9=其他
   报汛等级(bxdj): 固定为'1,2,3,4,5,'

💡 重要说明：
   - bxdj参数固定不变
   - 工具会自动处理会话认证
   - 返回的HTML包含完整水情数据
   - 支持全浙江省各市县查询

🎯 项目完成！
   一个文件搞定所有功能，简单易用！
