#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NUXT数据解析器
专门解析window.__NUXT__中的水情数据
"""

import requests
import json
import re
from optimized_water_crawler import OptimizedWaterCrawler
from datetime import datetime


class NuxtWaterDataParser:
    """NUXT水情数据解析器"""
    
    def __init__(self):
        self.crawler = OptimizedWaterCrawler()
    
    def get_nuxt_data(self, city="台州市", district="黄岩区"):
        """获取并解析NUXT数据"""
        print(f"🌊 获取 {city} {district} 的NUXT数据...")
        
        # 创建会话
        session = requests.Session()
        session.headers.update(self.crawler.session.headers)
        
        try:
            # 初始化会话
            session.get("https://sqfb.slt.zj.gov.cn/", timeout=30)
            session.get("https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/", timeout=30)
            
            # 构建参数
            params = {
                'areaFlag': 1,
                'sss': city,
                'ssx': district,
                'zl': 'RR,ZZ,ZQ,DD,TT,',
                'sklx': '4,5,3,2,1,9,',
                'sfcj': 1,
                'bxdj': '1,2,3,4,5,',
                'ly': '',
                'zm': '',
                'cjly': '',
                'bx': 0
            }
            
            url = "https://sqfb.slt.zj.gov.cn:30050/nuxtsyq/new/realtimeWater"
            response = session.get(url, params=params, timeout=30)
            
            print(f"   状态码: {response.status_code}")
            print(f"   内容长度: {len(response.text)} 字符")
            
            return response.text
            
        except Exception as e:
            print(f"❌ 获取数据失败: {str(e)}")
            return None
    
    def parse_nuxt_script(self, html_content):
        """解析NUXT脚本中的数据"""
        print(f"🔍 解析NUXT脚本数据...")

        if not html_content:
            return None

        # 多种模式尝试查找window.__NUXT__脚本
        patterns = [
            r'window\.__NUXT__=\(function\([^)]*\)\{return\s+(\{.*?\})\}\(([^)]*)\)\);',
            r'window\.__NUXT__=\(function\([^)]*\)\{return(\{.*?\})\}\(([^)]*)\)\);',
            r'window\.__NUXT__=.*?return\s*(\{.*?\}).*?\}\(([^)]*)\)\);'
        ]

        for i, pattern in enumerate(patterns):
            print(f"   尝试模式 {i+1}...")
            matches = re.findall(pattern, html_content, re.DOTALL)

            if matches:
                print(f"   ✅ 模式 {i+1} 找到NUXT数据")

                if len(matches[0]) == 2:
                    nuxt_data_str, params_str = matches[0]
                else:
                    nuxt_data_str = matches[0]
                    params_str = ""

                print(f"   📋 数据结构长度: {len(nuxt_data_str)} 字符")
                print(f"   📋 参数长度: {len(params_str)} 字符")

                if params_str:
                    # 解析参数
                    params = self.parse_function_params(params_str)

                    # 替换数据结构中的变量
                    parsed_data = self.substitute_variables(nuxt_data_str, params)

                    return parsed_data
                else:
                    # 尝试直接解析
                    try:
                        parsed = json.loads(nuxt_data_str)
                        return parsed
                    except:
                        continue

        # 如果正则匹配失败，尝试简单的字符串搜索
        print("   🔍 尝试简单字符串搜索...")
        if 'window.__NUXT__=' in html_content:
            start_idx = html_content.find('window.__NUXT__=')
            if start_idx != -1:
                # 找到结束位置
                end_idx = html_content.find(';</script>', start_idx)
                if end_idx != -1:
                    nuxt_script = html_content[start_idx:end_idx + 1]
                    print(f"   ✅ 找到NUXT脚本: {len(nuxt_script)} 字符")
                    print(f"   📋 脚本预览: {nuxt_script[:200]}...")

                    # 手动解析这个脚本
                    return self.manual_parse_nuxt(nuxt_script)

        print("   ❌ 未找到NUXT数据")
        return None

    def manual_parse_nuxt(self, nuxt_script):
        """手动解析NUXT脚本"""
        print(f"   🔧 手动解析NUXT脚本...")

        try:
            # 根据您提供的实际格式进行解析
            # 查找函数参数部分
            if '(null,"黄岩区"' in nuxt_script:
                print("   ✅ 找到参数模式")

                # 提取参数部分
                param_start = nuxt_script.rfind('(')
                param_end = nuxt_script.rfind(')')

                if param_start != -1 and param_end != -1:
                    params_str = nuxt_script[param_start+1:param_end]
                    print(f"   📋 参数字符串: {params_str[:100]}...")

                    # 解析参数
                    params = self.parse_function_params(params_str)
                    print(f"   📋 解析出参数: {len(params)} 个")

                    # 构建数据结构（基于您提供的格式）
                    water_data = self.build_water_data_from_params(params)

                    return water_data

            return None

        except Exception as e:
            print(f"   ❌ 手动解析失败: {str(e)}")
            return None

    def build_water_data_from_params(self, params):
        """根据参数构建水情数据结构"""
        print(f"   🏗️ 构建水情数据结构...")

        try:
            # 根据您提供的实际数据格式构建
            # 参数顺序：null,"黄岩区","-","1","台-黄岩","08-05T00:15:00","椒江水系","PZ","331003","RR","浙江省","台州市","2025-08-05T00:15:00","-0.0"," 0","70405712","飞水岩水库","153.67",121.097038,28.667364,"70405742","水竹水库","210.69",121.195051,28.71574,"2",-.01,"70405744","黄坦水库","18.31",121.196961,28.699041,"7041JB44","白沙园水库","93.38",121.031007,28.495483,"7041JB46","柔极溪二级水库","354.02",120.940955,28.683597

            if len(params) < 20:
                print(f"   ❌ 参数数量不足: {len(params)}")
                return None

            # 基础信息
            county = params[1] if len(params) > 1 else ""
            city_code = params[4] if len(params) > 4 else ""
            time_str = params[5] if len(params) > 5 else ""
            water_system = params[6] if len(params) > 6 else ""
            area_code = params[8] if len(params) > 8 else ""
            station_type = params[9] if len(params) > 9 else ""
            province = params[10] if len(params) > 10 else ""
            city = params[11] if len(params) > 11 else ""

            print(f"   📍 基础信息: {province} {city} {county}")

            # 解析水库数据（从参数15开始，每6个参数一组）
            stations = []
            i = 15
            station_index = 1

            while i + 5 < len(params):
                try:
                    station_id = params[i]
                    station_name = params[i + 1]
                    water_level = params[i + 2]
                    longitude = params[i + 3]
                    latitude = params[i + 4]

                    station = {
                        'index': station_index,
                        'zh': station_id,
                        'name': station_name,
                        'city': city,
                        'county': county,
                        'time': time_str,
                        'sw': water_level,
                        'xx': None,  # 限制水位
                        'kr': None,  # 库容
                        'zc': None,  # 涨潮
                        'lon': longitude,
                        'lat': latitude,
                        'info': {
                            'zh': station_id,
                            'zm': station_name,
                            'ly': water_system,
                            'pro': province,
                            'sss': city,
                            'ssx': county,
                            'sw': water_level,
                            'wd': latitude,
                            'jd': longitude,
                            'zl': station_type
                        }
                    }

                    stations.append(station)
                    station_index += 1
                    i += 6  # 下一个站点

                except Exception as e:
                    print(f"   ⚠️ 解析站点 {station_index} 时出错: {str(e)}")
                    break

            print(f"   ✅ 成功解析 {len(stations)} 个站点")

            # 构建完整的数据结构
            water_data = {
                'layout': 'default',
                'data': [{
                    'waterData': {
                        'cjj': [],
                        'qt': [],
                        'cx': stations
                    },
                    'ThData': None
                }],
                'fetch': {},
                'error': None,
                'serverRendered': True,
                'routePath': '/new/realtimeWater'
            }

            return water_data

        except Exception as e:
            print(f"   ❌ 构建数据结构失败: {str(e)}")
            return None
    
    def parse_function_params(self, params_str):
        """解析函数参数"""
        print(f"   🔧 解析函数参数...")
        
        # 移除括号并按逗号分割
        params_str = params_str.strip()
        if params_str.startswith('('):
            params_str = params_str[1:]
        if params_str.endswith(')'):
            params_str = params_str[:-1]
        
        # 分割参数，处理字符串中的逗号
        params = []
        current_param = ""
        in_string = False
        escape_next = False
        
        for char in params_str:
            if escape_next:
                current_param += char
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                current_param += char
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                current_param += char
                continue
                
            if char == ',' and not in_string:
                params.append(current_param.strip())
                current_param = ""
                continue
                
            current_param += char
        
        if current_param.strip():
            params.append(current_param.strip())
        
        # 处理参数值
        processed_params = []
        for param in params:
            param = param.strip()
            if param == 'null':
                processed_params.append(None)
            elif param.startswith('"') and param.endswith('"'):
                # 字符串参数，移除引号并处理转义
                processed_params.append(param[1:-1].replace('\\"', '"').replace('\\\\', '\\'))
            elif param.replace('.', '').replace('-', '').isdigit():
                # 数字参数
                if '.' in param:
                    processed_params.append(float(param))
                else:
                    processed_params.append(int(param))
            else:
                processed_params.append(param)
        
        print(f"      解析出 {len(processed_params)} 个参数")
        return processed_params
    
    def substitute_variables(self, data_str, params):
        """替换数据结构中的变量"""
        print(f"   🔄 替换变量...")
        
        # 创建变量映射 (a, b, c, d, ...)
        var_map = {}
        alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
        
        for i, param in enumerate(params):
            if i < len(alphabet):
                var_map[alphabet[i]] = param
        
        print(f"      变量映射: {len(var_map)} 个变量")
        
        # 替换变量
        result = data_str
        for var, value in var_map.items():
            if value is None:
                result = result.replace(var, 'null')
            elif isinstance(value, str):
                # 转义字符串中的特殊字符
                escaped_value = json.dumps(value, ensure_ascii=False)
                result = result.replace(var, escaped_value)
            else:
                result = result.replace(var, str(value))
        
        try:
            # 尝试解析为JSON
            parsed = json.loads(result)
            print(f"   ✅ 成功解析为JSON")
            return parsed
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {str(e)}")
            print(f"   原始数据片段: {result[:200]}...")
            return None
    
    def extract_water_stations(self, nuxt_data):
        """提取水情站点数据"""
        print(f"🏭 提取水情站点数据...")
        
        if not nuxt_data:
            return []
        
        stations = []
        
        try:
            # 导航到水情数据
            data_list = nuxt_data.get('data', [])
            if not data_list:
                print("   ❌ 未找到data数组")
                return []
            
            water_data = data_list[0].get('waterData', {})
            if not water_data:
                print("   ❌ 未找到waterData")
                return []
            
            # 提取不同类型的站点
            station_types = {
                'cx': '测站',
                'cjj': '采集井', 
                'qt': '其他'
            }
            
            for type_key, type_name in station_types.items():
                type_stations = water_data.get(type_key, [])
                print(f"   📍 {type_name}: {len(type_stations)} 个站点")
                
                for station in type_stations:
                    station_info = {
                        'type': type_name,
                        'index': station.get('index'),
                        'zh': station.get('zh'),  # 站号
                        'name': station.get('name'),  # 站名
                        'city': station.get('city'),  # 城市
                        'county': station.get('county'),  # 区县
                        'time': station.get('time'),  # 时间
                        'sw': station.get('sw'),  # 水位
                        'xx': station.get('xx'),  # 限制水位
                        'kr': station.get('kr'),  # 库容
                        'zc': station.get('zc'),  # 涨潮
                        'lon': station.get('lon'),  # 经度
                        'lat': station.get('lat'),  # 纬度
                        'info': station.get('info', {})  # 详细信息
                    }
                    stations.append(station_info)
            
            # 提取ThData（可能是汇总数据）
            th_data = data_list[0].get('ThData')
            if th_data:
                print(f"   📊 汇总数据: {th_data}")
            
            print(f"   ✅ 总共提取 {len(stations)} 个站点")
            return stations
            
        except Exception as e:
            print(f"   ❌ 提取失败: {str(e)}")
            return []
    
    def format_station_data(self, stations):
        """格式化站点数据"""
        print(f"📋 格式化站点数据...")
        
        formatted_stations = []
        
        for station in stations:
            formatted = {
                '站点类型': station['type'],
                '站点编号': station['zh'],
                '站点名称': station['name'],
                '所属城市': station['city'],
                '所属区县': station['county'],
                '更新时间': station['time'],
                '当前水位': f"{station['sw']} m" if station['sw'] else "无数据",
                '限制水位': f"{station['xx']} m" if station['xx'] else "无数据",
                '库容': f"{station['kr']} 万m³" if station['kr'] else "无数据",
                '涨潮值': f"{station['zc']} m" if station['zc'] else "无数据",
                '经度': station['lon'],
                '纬度': station['lat'],
                '详细信息': station['info']
            }
            formatted_stations.append(formatted)
        
        return formatted_stations
    
    def get_water_data(self, city="台州市", district="黄岩区"):
        """获取完整的水情数据"""
        print(f"🌊 开始获取 {city} {district} 的完整水情数据")
        print("="*60)
        
        # 1. 获取HTML内容
        html_content = self.get_nuxt_data(city, district)
        if not html_content:
            return None
        
        # 2. 解析NUXT数据
        nuxt_data = self.parse_nuxt_script(html_content)
        if not nuxt_data:
            return None
        
        # 3. 提取站点数据
        stations = self.extract_water_stations(nuxt_data)
        if not stations:
            return None
        
        # 4. 格式化数据
        formatted_stations = self.format_station_data(stations)
        
        # 5. 组装结果
        result = {
            'query': {
                'city': city,
                'district': district,
                'timestamp': datetime.now().isoformat()
            },
            'raw_nuxt_data': nuxt_data,
            'stations': formatted_stations,
            'summary': {
                'total_stations': len(stations),
                'station_types': {}
            }
        }
        
        # 统计站点类型
        for station in stations:
            station_type = station['type']
            if station_type not in result['summary']['station_types']:
                result['summary']['station_types'][station_type] = 0
            result['summary']['station_types'][station_type] += 1
        
        # 6. 保存结果
        output_file = f'water_stations_{city}_{district}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n🎯 数据提取完成!")
        print(f"   总站点数: {result['summary']['total_stations']}")
        print(f"   站点类型: {result['summary']['station_types']}")
        print(f"💾 数据已保存到: {output_file}")
        
        return result


def main():
    """主函数"""
    print("🌊 NUXT水情数据解析器")
    print("专门解析window.__NUXT__中的水情数据")
    print("="*60)
    
    parser = NuxtWaterDataParser()
    
    # 解析台州市黄岩区的数据
    result = parser.get_water_data("台州市", "黄岩区")
    
    if result:
        print(f"\n📊 解析结果:")
        print(f"   查询地区: {result['query']['city']} {result['query']['district']}")
        print(f"   查询时间: {result['query']['timestamp']}")
        print(f"   总站点数: {result['summary']['total_stations']}")
        
        print(f"\n🏭 站点详情:")
        for i, station in enumerate(result['stations'][:5]):  # 只显示前5个
            print(f"   站点 {i+1}:")
            print(f"      名称: {station['站点名称']}")
            print(f"      类型: {station['站点类型']}")
            print(f"      水位: {station['当前水位']}")
            print(f"      位置: {station['经度']}, {station['纬度']}")
        
        if len(result['stations']) > 5:
            print(f"   ... 还有 {len(result['stations']) - 5} 个站点")
    
    print(f"\n✅ 解析完成!")


if __name__ == "__main__":
    main()
