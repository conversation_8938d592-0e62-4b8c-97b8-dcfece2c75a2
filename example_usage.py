#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水情数据获取示例
简单的使用示例和数据处理
"""

from water_info_crawler import WaterInfoCrawler
import json
import time
from datetime import datetime


def format_water_data(data):
    """格式化水情数据输出"""
    if not data:
        return "无数据"
    
    if data.get('status') == 'html_response':
        return f"收到HTML响应，可能需要会话认证\n完整URL: {data.get('full_url')}"
    
    # 如果是JSON数据，尝试格式化输出
    try:
        formatted = json.dumps(data, ensure_ascii=False, indent=2)
        return formatted
    except:
        return str(data)


def get_multiple_areas_data():
    """获取多个地区的水情数据"""
    crawler = WaterInfoCrawler()
    
    # 定义要查询的地区
    areas = [
        ("台州市", "黄岩区"),
        ("台州市", "椒江区"),
        ("台州市", "路桥区"),
        ("杭州市", "西湖区"),
        ("宁波市", "海曙区")
    ]
    
    results = {}
    
    for city, district in areas:
        print(f"\n正在获取 {city} {district} 的水情数据...")
        
        try:
            data = crawler.get_realtime_water_data(city, district)
            results[f"{city}-{district}"] = data
            
            if data:
                print(f"✓ {city} {district} 数据获取成功")
                if data.get('status') == 'html_response':
                    print("  注意：收到HTML响应，可能需要会话认证")
            else:
                print(f"✗ {city} {district} 数据获取失败")
                
        except Exception as e:
            print(f"✗ {city} {district} 获取出错: {str(e)}")
            results[f"{city}-{district}"] = None
        
        # 添加请求间隔，避免频繁请求
        time.sleep(2)
    
    return results


def save_data_to_file(data, filename=None):
    """保存数据到文件"""
    if not filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"water_data_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filename}")
        return True
    except Exception as e:
        print(f"保存文件失败: {str(e)}")
        return False


def search_specific_stations():
    """搜索特定类型的站点"""
    crawler = WaterInfoCrawler()
    
    # 搜索关键词
    keywords = ["水库", "河道", "闸站", "泵站"]
    
    results = {}
    
    for keyword in keywords:
        print(f"\n搜索包含 '{keyword}' 的站点...")
        
        try:
            data = crawler.search_stations_by_name(keyword, "台州市")
            results[keyword] = data
            
            if data:
                print(f"✓ 找到包含 '{keyword}' 的站点数据")
            else:
                print(f"✗ 未找到包含 '{keyword}' 的站点")
                
        except Exception as e:
            print(f"✗ 搜索 '{keyword}' 时出错: {str(e)}")
            results[keyword] = None
        
        time.sleep(1)
    
    return results


def monitor_warning_levels():
    """监控预警级别数据"""
    crawler = WaterInfoCrawler()
    
    # 不同预警级别
    warning_configs = [
        ("1,2", "低级预警"),
        ("3", "中级预警"),
        ("4,5", "高级预警")
    ]
    
    results = {}
    
    for levels, description in warning_configs:
        print(f"\n获取 {description} (等级: {levels}) 数据...")
        
        try:
            data = crawler.get_warning_data("台州市", levels)
            results[description] = data
            
            if data:
                print(f"✓ {description} 数据获取成功")
            else:
                print(f"✗ {description} 数据获取失败")
                
        except Exception as e:
            print(f"✗ 获取 {description} 时出错: {str(e)}")
            results[description] = None
        
        time.sleep(1)
    
    return results


def main():
    """主函数"""
    print("=== 水情数据获取示例程序 ===\n")
    
    while True:
        print("\n请选择操作:")
        print("1. 获取单个地区水情数据")
        print("2. 获取多个地区水情数据")
        print("3. 搜索特定站点")
        print("4. 监控预警级别")
        print("5. 使用会话ID获取数据")
        print("0. 退出程序")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("程序退出")
            break
            
        elif choice == '1':
            city = input("请输入城市名称 (默认: 台州市): ").strip() or "台州市"
            district = input("请输入区县名称 (默认: 黄岩区): ").strip() or "黄岩区"
            
            crawler = WaterInfoCrawler()
            print(f"\n正在获取 {city} {district} 的水情数据...")
            
            data = crawler.get_realtime_water_data(city, district)
            print("\n=== 获取结果 ===")
            print(format_water_data(data))
            
            if data and input("\n是否保存到文件? (y/n): ").lower() == 'y':
                save_data_to_file({f"{city}-{district}": data})
        
        elif choice == '2':
            print("\n正在获取多个地区的水情数据...")
            results = get_multiple_areas_data()
            
            print("\n=== 汇总结果 ===")
            for area, data in results.items():
                status = "成功" if data else "失败"
                print(f"{area}: {status}")
            
            if any(results.values()) and input("\n是否保存所有数据到文件? (y/n): ").lower() == 'y':
                save_data_to_file(results)
        
        elif choice == '3':
            print("\n正在搜索特定站点...")
            results = search_specific_stations()
            
            print("\n=== 搜索结果 ===")
            for keyword, data in results.items():
                status = "找到数据" if data else "无数据"
                print(f"关键词 '{keyword}': {status}")
            
            if any(results.values()) and input("\n是否保存搜索结果到文件? (y/n): ").lower() == 'y':
                save_data_to_file(results)
        
        elif choice == '4':
            print("\n正在监控预警级别...")
            results = monitor_warning_levels()
            
            print("\n=== 预警监控结果 ===")
            for level, data in results.items():
                status = "有数据" if data else "无数据"
                print(f"{level}: {status}")
            
            if any(results.values()) and input("\n是否保存预警数据到文件? (y/n): ").lower() == 'y':
                save_data_to_file(results)
        
        elif choice == '5':
            session_id = input("请输入JSESSIONID: ").strip()
            if session_id:
                crawler = WaterInfoCrawler()
                print(f"\n使用会话ID获取数据...")
                
                data = crawler.get_water_data_with_session(session_id)
                print("\n=== 获取结果 ===")
                print(format_water_data(data))
                
                if data and input("\n是否保存到文件? (y/n): ").lower() == 'y':
                    save_data_to_file({"session_data": data})
            else:
                print("未输入会话ID")
        
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
