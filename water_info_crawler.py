#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水情数据获取工具
基于抓包分析的浙江省水利厅水情数据获取
"""

import requests
import json
import urllib.parse
from typing import Dict, List, Optional
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WaterInfoCrawler:
    """水情数据爬虫类"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-User': '?1',
            'Sec-Fetch-Dest': 'iframe',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def get_realtime_water_data(self, 
                               city: str = "台州市", 
                               district: str = "黄岩区",
                               area_flag: int = 1,
                               station_types: str = "RR,ZZ,ZQ,DD,TT,",
                               reservoir_types: str = "4,5,3,1,9,2,",
                               is_over_warning: int = 1,
                               warning_levels: str = "1,2,3,4,5,",
                               station_name: str = "",
                               collection_source: str = "",
                               warning_flag: int = 0) -> Optional[Dict]:
        """
        获取实时水情数据
        
        Args:
            city: 城市名称，如"台州市"
            district: 区县名称，如"黄岩区"
            area_flag: 区域标志，默认1
            station_types: 站点类型，如"RR,ZZ,ZQ,DD,TT,"
            reservoir_types: 水库类型，如"4,5,3,1,9,2,"
            is_over_warning: 是否超警，默认1
            warning_levels: 报汛等级，如"1,2,3,4,5,"
            station_name: 站名筛选
            collection_source: 采集来源
            warning_flag: 报汛标志，默认0
            
        Returns:
            返回水情数据字典，失败返回None
        """
        
        # 构建请求参数
        params = {
            'areaFlag': area_flag,
            'sss': city,
            'ssx': district,
            'zl': station_types,
            'sklx': reservoir_types,
            'ly': '',
            'sfcj': is_over_warning,
            'bxdj': warning_levels,
            'zm': station_name,
            'cjly': collection_source,
            'bx': warning_flag
        }
        
        # 构建完整URL
        url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
        
        try:
            logger.info(f"正在获取水情数据: {city} - {district}")
            logger.info(f"请求URL: {url}")
            logger.info(f"请求参数: {params}")
            
            # 发送GET请求
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            logger.info(f"请求成功，状态码: {response.status_code}")
            logger.info(f"响应内容类型: {response.headers.get('Content-Type', 'unknown')}")
            
            # 检查响应内容类型
            content_type = response.headers.get('Content-Type', '')
            
            if 'application/json' in content_type:
                # JSON响应
                return response.json()
            elif 'text/html' in content_type:
                # HTML响应，可能需要进一步解析
                logger.warning("收到HTML响应，可能需要会话认证或页面解析")
                return {
                    'status': 'html_response',
                    'content': response.text[:1000],  # 只返回前1000字符
                    'full_url': response.url
                }
            else:
                # 其他类型响应
                logger.warning(f"未知响应类型: {content_type}")
                return {
                    'status': 'unknown_response',
                    'content_type': content_type,
                    'content': response.text[:500]
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return None
    
    def get_water_data_with_session(self, session_id: str = None) -> Optional[Dict]:
        """
        使用指定的会话ID获取水情数据
        
        Args:
            session_id: JSESSIONID值
            
        Returns:
            返回水情数据字典，失败返回None
        """
        if session_id:
            # 设置Cookie
            self.session.cookies.set('JSESSIONID', session_id)
            self.session.cookies.set('cna', '3VwWIVY7zHMCAf////8R5Gmj')
            self.session.cookies.set('zh_choose_undefined', 's')
        
        return self.get_realtime_water_data()
    
    def search_stations_by_name(self, station_name: str, city: str = "台州市") -> Optional[Dict]:
        """
        根据站点名称搜索水情数据
        
        Args:
            station_name: 站点名称
            city: 城市名称
            
        Returns:
            返回搜索结果
        """
        return self.get_realtime_water_data(
            city=city,
            district="",  # 不限制区县
            station_name=station_name
        )
    
    def get_warning_data(self, city: str = "台州市", warning_levels: str = "4,5") -> Optional[Dict]:
        """
        获取预警级别的水情数据
        
        Args:
            city: 城市名称
            warning_levels: 预警等级，如"4,5"表示4级和5级预警
            
        Returns:
            返回预警数据
        """
        return self.get_realtime_water_data(
            city=city,
            district="",
            warning_levels=warning_levels,
            is_over_warning=1
        )


def main():
    """主函数，演示如何使用水情数据爬虫"""
    
    # 创建爬虫实例
    crawler = WaterInfoCrawler()
    
    print("=== 浙江省水情数据获取工具 ===\n")
    
    # 示例1：获取台州市黄岩区的水情数据
    print("1. 获取台州市黄岩区水情数据...")
    data = crawler.get_realtime_water_data("台州市", "黄岩区")
    if data:
        print(f"数据获取成功！")
        print(f"数据类型: {data.get('status', 'json_data')}")
        if 'content' in data:
            print(f"响应内容预览: {data['content'][:200]}...")
        else:
            print(f"数据内容: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
    else:
        print("数据获取失败！")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2：搜索特定站点
    print("2. 搜索包含'水库'的站点...")
    station_data = crawler.search_stations_by_name("水库", "台州市")
    if station_data:
        print("站点搜索成功！")
        print(f"搜索结果类型: {station_data.get('status', 'json_data')}")
    else:
        print("站点搜索失败！")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3：获取预警数据
    print("3. 获取高级别预警数据...")
    warning_data = crawler.get_warning_data("台州市", "4,5")
    if warning_data:
        print("预警数据获取成功！")
        print(f"预警数据类型: {warning_data.get('status', 'json_data')}")
    else:
        print("预警数据获取失败！")
    
    print("\n=== 使用说明 ===")
    print("1. 如果返回HTML响应，可能需要先访问主页获取有效的会话ID")
    print("2. 可以使用 get_water_data_with_session() 方法传入有效的JSESSIONID")
    print("3. 参数可以根据需要调整，支持不同城市、区县、站点类型等")
    print("4. 建议添加适当的请求间隔，避免频繁请求")


if __name__ == "__main__":
    main()
