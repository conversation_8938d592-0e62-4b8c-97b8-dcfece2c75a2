#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水情数据获取工具 - 完整功能测试
测试所有主要功能并生成测试报告
"""

import json
import time
from datetime import datetime
from water_info_crawler import WaterInfoCrawler
from advanced_water_crawler import AdvancedWaterCrawler


def test_basic_crawler():
    """测试基础版爬虫"""
    print("=" * 60)
    print("测试基础版爬虫 (WaterInfoCrawler)")
    print("=" * 60)
    
    crawler = WaterInfoCrawler()
    results = {}
    
    # 测试1: 基本数据获取
    print("\n1. 测试基本数据获取...")
    try:
        data = crawler.get_realtime_water_data("台州市", "黄岩区")
        results['basic_data'] = {
            'success': data is not None,
            'status': data.get('status') if data else None,
            'has_content': bool(data.get('content')) if data else False
        }
        print(f"   结果: {'✓ 成功' if data else '✗ 失败'}")
        if data:
            print(f"   状态: {data.get('status', 'unknown')}")
    except Exception as e:
        results['basic_data'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    # 测试2: 站点搜索
    print("\n2. 测试站点搜索...")
    try:
        data = crawler.search_stations_by_name("水库", "台州市")
        results['station_search'] = {
            'success': data is not None,
            'status': data.get('status') if data else None
        }
        print(f"   结果: {'✓ 成功' if data else '✗ 失败'}")
    except Exception as e:
        results['station_search'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    # 测试3: 预警数据
    print("\n3. 测试预警数据获取...")
    try:
        data = crawler.get_warning_data("台州市", "4,5")
        results['warning_data'] = {
            'success': data is not None,
            'status': data.get('status') if data else None
        }
        print(f"   结果: {'✓ 成功' if data else '✗ 失败'}")
    except Exception as e:
        results['warning_data'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    return results


def test_advanced_crawler():
    """测试增强版爬虫"""
    print("\n" + "=" * 60)
    print("测试增强版爬虫 (AdvancedWaterCrawler)")
    print("=" * 60)
    
    crawler = AdvancedWaterCrawler()
    results = {}
    
    # 测试1: 综合数据获取
    print("\n1. 测试综合数据获取...")
    try:
        data = crawler.get_comprehensive_water_data("台州市", "黄岩区")
        results['comprehensive_data'] = {
            'success': data is not None,
            'status': data.get('status') if data else None,
            'has_parsed_data': bool(data.get('parsed_data')) if data else False
        }
        print(f"   结果: {'✓ 成功' if data else '✗ 失败'}")
        if data:
            print(f"   状态: {data.get('status', 'unknown')}")
            if data.get('parsed_data'):
                print(f"   解析数据: {data['parsed_data']}")
    except Exception as e:
        results['comprehensive_data'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    # 测试2: 多地区测试
    print("\n2. 测试多地区数据获取...")
    test_areas = [("台州市", "黄岩区"), ("杭州市", "西湖区")]
    multi_results = {}
    
    for city, district in test_areas:
        try:
            print(f"   测试 {city} {district}...")
            data = crawler.get_comprehensive_water_data(city, district)
            multi_results[f"{city}-{district}"] = {
                'success': data is not None,
                'status': data.get('status') if data else None
            }
            print(f"     结果: {'✓ 成功' if data else '✗ 失败'}")
            time.sleep(1)  # 避免频繁请求
        except Exception as e:
            multi_results[f"{city}-{district}"] = {'success': False, 'error': str(e)}
            print(f"     结果: ✗ 异常 - {str(e)}")
    
    results['multi_area'] = multi_results
    
    return results


def test_error_handling():
    """测试错误处理"""
    print("\n" + "=" * 60)
    print("测试错误处理能力")
    print("=" * 60)
    
    crawler = WaterInfoCrawler()
    results = {}
    
    # 测试1: 无效城市名
    print("\n1. 测试无效城市名...")
    try:
        data = crawler.get_realtime_water_data("不存在的城市", "不存在的区")
        results['invalid_city'] = {
            'success': data is not None,
            'handled_gracefully': True
        }
        print(f"   结果: ✓ 优雅处理")
    except Exception as e:
        results['invalid_city'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    # 测试2: 空参数
    print("\n2. 测试空参数...")
    try:
        data = crawler.get_realtime_water_data("", "")
        results['empty_params'] = {
            'success': data is not None,
            'handled_gracefully': True
        }
        print(f"   结果: ✓ 优雅处理")
    except Exception as e:
        results['empty_params'] = {'success': False, 'error': str(e)}
        print(f"   结果: ✗ 异常 - {str(e)}")
    
    return results


def generate_test_report(basic_results, advanced_results, error_results):
    """生成测试报告"""
    report = {
        'test_time': datetime.now().isoformat(),
        'basic_crawler': basic_results,
        'advanced_crawler': advanced_results,
        'error_handling': error_results,
        'summary': {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0
        }
    }
    
    # 统计测试结果
    all_results = [basic_results, advanced_results, error_results]
    for result_group in all_results:
        for test_name, test_result in result_group.items():
            report['summary']['total_tests'] += 1
            if isinstance(test_result, dict):
                if test_result.get('success', False):
                    report['summary']['passed_tests'] += 1
                else:
                    report['summary']['failed_tests'] += 1
            elif isinstance(test_result, dict) and 'multi_area' in result_group:
                # 处理多地区测试结果
                for area_result in test_result.values():
                    report['summary']['total_tests'] += 1
                    if area_result.get('success', False):
                        report['summary']['passed_tests'] += 1
                    else:
                        report['summary']['failed_tests'] += 1
    
    return report


def save_test_report(report):
    """保存测试报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"test_report_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n测试报告已保存到: {filename}")
        return True
    except Exception as e:
        print(f"\n保存测试报告失败: {str(e)}")
        return False


def print_summary(report):
    """打印测试摘要"""
    print("\n" + "=" * 60)
    print("测试摘要")
    print("=" * 60)
    
    summary = report['summary']
    print(f"总测试数: {summary['total_tests']}")
    print(f"通过测试: {summary['passed_tests']}")
    print(f"失败测试: {summary['failed_tests']}")
    
    if summary['total_tests'] > 0:
        success_rate = (summary['passed_tests'] / summary['total_tests']) * 100
        print(f"成功率: {success_rate:.1f}%")
    
    print(f"\n测试时间: {report['test_time']}")
    
    # 详细结果
    print("\n详细结果:")
    print("基础版爬虫:")
    for test_name, result in report['basic_crawler'].items():
        status = "✓" if result.get('success', False) else "✗"
        print(f"  {status} {test_name}")
    
    print("增强版爬虫:")
    for test_name, result in report['advanced_crawler'].items():
        if test_name == 'multi_area':
            print(f"  多地区测试:")
            for area, area_result in result.items():
                status = "✓" if area_result.get('success', False) else "✗"
                print(f"    {status} {area}")
        else:
            status = "✓" if result.get('success', False) else "✗"
            print(f"  {status} {test_name}")
    
    print("错误处理:")
    for test_name, result in report['error_handling'].items():
        status = "✓" if result.get('success', False) else "✗"
        print(f"  {status} {test_name}")


def main():
    """主测试函数"""
    print("浙江省水情数据获取工具 - 完整功能测试")
    print("测试开始时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 执行所有测试
    basic_results = test_basic_crawler()
    advanced_results = test_advanced_crawler()
    error_results = test_error_handling()
    
    # 生成测试报告
    report = generate_test_report(basic_results, advanced_results, error_results)
    
    # 打印摘要
    print_summary(report)
    
    # 保存报告
    save_test_report(report)
    
    print("\n测试完成！")


if __name__ == "__main__":
    main()
