#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于正确字段定义的水情查询工具
根据用户提供的准确字段说明开发
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional


class FieldBasedQueryTool:
    """基于字段定义的查询工具"""
    
    def __init__(self):
        self.base_url = "https://sqfb.slt.zj.gov.cn:30050"
        self.main_url = "https://sqfb.slt.zj.gov.cn/"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://sqfb.slt.zj.gov.cn/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
        
        # 字段定义（基于用户提供的准确信息）
        self.field_definitions = {
            'station_types': {
                'RR': '水库',
                'ZZ': '河道',
                'ZQ': '河道',
                'DD': '堰闸',
                'TT': '潮汐'
            },
            'reservoir_types': {
                '1': '其他(含小二)',
                '2': '小一',
                '3': '中型水库',
                '4': '大型水库',
                '5': '大型水库',
                '9': '其他(含小二)'
            },
            'warning_levels': {
                '1': '蓝色预警',
                '2': '黄色预警',
                '3': '橙色预警',
                '4': '红色预警',
                '5': '特级预警'
            }
        }
    
    def initialize_session(self) -> bool:
        """初始化会话"""
        try:
            print("🔐 初始化会话...")
            
            # 访问主页
            response = self.session.get(self.main_url, timeout=30)
            response.raise_for_status()
            
            # 访问水情页面
            water_page_url = f"{self.base_url}/nuxtsyq/"
            response = self.session.get(water_page_url, timeout=30)
            response.raise_for_status()
            
            print(f"   ✅ 会话初始化成功")
            return True
            
        except Exception as e:
            print(f"   ❌ 会话初始化失败: {str(e)}")
            return False
    
    def query_by_station_type(self, station_types: List[str], city: str = "台州市", district: str = "黄岩区") -> Dict:
        """按站点类型查询"""
        print(f"🏭 按站点类型查询: {[self.field_definitions['station_types'][t] for t in station_types]}")
        
        # 构建zl参数
        zl_param = ','.join(station_types) + ','
        
        params = {
            'areaFlag': 1,
            'sss': city,
            'ssx': district,
            'zl': zl_param,
            'sklx': '4,5,3,2,1,9,',  # 所有水库类型
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
        
        return self._execute_query(params, f"站点类型查询_{'+'.join(station_types)}")
    
    def query_by_reservoir_type(self, reservoir_types: List[str], city: str = "台州市", district: str = "黄岩区") -> Dict:
        """按水库类型查询"""
        print(f"🏗️ 按水库类型查询: {[self.field_definitions['reservoir_types'][t] for t in reservoir_types]}")
        
        # 构建sklx参数
        sklx_param = ','.join(reservoir_types) + ','
        
        params = {
            'areaFlag': 1,
            'sss': city,
            'ssx': district,
            'zl': 'RR,ZZ,ZQ,DD,TT,',  # 所有站点类型
            'sklx': sklx_param,
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
        
        return self._execute_query(params, f"水库类型查询_{'+'.join(reservoir_types)}")
    
    def query_by_warning_level(self, warning_levels: List[str], city: str = "台州市", district: str = "黄岩区") -> Dict:
        """按预警等级查询"""
        print(f"🚨 按预警等级查询: {[self.field_definitions['warning_levels'][w] for w in warning_levels]}")
        
        # 构建bxdj参数
        bxdj_param = ','.join(warning_levels) + ','
        
        params = {
            'areaFlag': 1,
            'sss': city,
            'ssx': district,
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': bxdj_param,
            'ly': '',
            'zm': '',
            'cjly': '',
            'bx': 0
        }
        
        return self._execute_query(params, f"预警等级查询_{'+'.join(warning_levels)}")
    
    def query_large_reservoirs(self, city: str = "台州市", district: str = "") -> Dict:
        """查询大型水库"""
        print(f"🏗️ 查询大型水库...")
        
        return self.query_by_reservoir_type(['4', '5'], city, district)
    
    def query_river_stations(self, city: str = "台州市", district: str = "") -> Dict:
        """查询河道站点"""
        print(f"🌊 查询河道站点...")
        
        return self.query_by_station_type(['ZZ', 'ZQ'], city, district)
    
    def query_high_warning(self, city: str = "台州市", district: str = "") -> Dict:
        """查询高级别预警"""
        print(f"🚨 查询高级别预警...")
        
        return self.query_by_warning_level(['4', '5'], city, district)
    
    def query_by_name(self, station_name: str, city: str = "台州市") -> Dict:
        """按站点名称查询"""
        print(f"🔍 按站点名称查询: {station_name}")
        
        params = {
            'areaFlag': 1,
            'sss': city,
            'ssx': '',  # 不限制区县
            'zl': 'RR,ZZ,ZQ,DD,TT,',
            'sklx': '4,5,3,2,1,9,',
            'sfcj': 1,
            'bxdj': '1,2,3,4,5,',
            'ly': '',
            'zm': station_name,
            'cjly': '',
            'bx': 0
        }
        
        return self._execute_query(params, f"站点名称查询_{station_name}")
    
    def _execute_query(self, params: Dict, query_name: str) -> Dict:
        """执行查询"""
        # 确保会话已初始化
        if not self.session.cookies:
            if not self.initialize_session():
                return {'error': '会话初始化失败'}
        
        try:
            url = f"{self.base_url}/nuxtsyq/new/realtimeWater"
            
            print(f"   📡 发送请求...")
            print(f"   📋 参数: {params}")
            
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            result = {
                'query_name': query_name,
                'query_params': params,
                'response': {
                    'status_code': response.status_code,
                    'content_length': len(response.text),
                    'url': response.url,
                    'success': True
                },
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"   ✅ 查询成功: {response.status_code}")
            print(f"   📄 内容长度: {len(response.text)} 字符")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 查询失败: {str(e)}")
            return {
                'query_name': query_name,
                'query_params': params,
                'response': {
                    'success': False,
                    'error': str(e)
                },
                'timestamp': datetime.now().isoformat()
            }
    
    def run_demo_queries(self):
        """运行演示查询"""
        print("🌊 基于字段定义的水情查询演示")
        print("="*60)
        
        results = {}
        
        # 1. 查询水库站点
        print("\n1. 查询水库站点...")
        results['水库站点'] = self.query_by_station_type(['RR'], "台州市", "黄岩区")
        
        # 2. 查询河道站点
        print("\n2. 查询河道站点...")
        results['河道站点'] = self.query_by_station_type(['ZZ', 'ZQ'], "台州市", "黄岩区")
        
        # 3. 查询大型水库
        print("\n3. 查询大型水库...")
        results['大型水库'] = self.query_large_reservoirs("台州市", "")
        
        # 4. 查询高级别预警
        print("\n4. 查询高级别预警...")
        results['高级别预警'] = self.query_high_warning("台州市", "")
        
        # 5. 按名称搜索
        print("\n5. 按名称搜索水库...")
        results['名称搜索'] = self.query_by_name("水库", "台州市")
        
        # 保存结果
        output_file = f"field_based_query_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 查询结果已保存到: {output_file}")
        
        # 显示摘要
        print(f"\n📊 查询摘要:")
        for query_name, result in results.items():
            if result.get('response', {}).get('success'):
                print(f"   ✅ {query_name}: 成功")
            else:
                print(f"   ❌ {query_name}: 失败")
        
        return results


def main():
    """主函数"""
    print("🌊 基于字段定义的水情查询工具")
    print("根据用户提供的准确字段说明开发")
    print("="*60)
    
    # 显示字段定义
    tool = FieldBasedQueryTool()
    
    print("📋 字段定义:")
    print("站点类型 (zl字段):")
    for code, name in tool.field_definitions['station_types'].items():
        print(f"   {code}: {name}")
    
    print("\n水库类型 (sklx字段):")
    for code, name in tool.field_definitions['reservoir_types'].items():
        print(f"   {code}: {name}")
    
    print("\n预警等级 (bxdj字段):")
    for code, name in tool.field_definitions['warning_levels'].items():
        print(f"   {code}: {name}")
    
    # 运行演示查询
    print("\n" + "="*60)
    results = tool.run_demo_queries()
    
    print(f"\n✅ 演示完成!")
    print(f"\n💡 说明:")
    print(f"   - 所有查询都使用了正确的字段定义")
    print(f"   - API调用参数已根据实际字段含义优化")
    print(f"   - 可以根据需要组合不同的查询条件")


if __name__ == "__main__":
    main()
